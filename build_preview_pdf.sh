#!/bin/bash

# 构建带水印的预览版PDF脚本
# 使用方法: ./build_preview_pdf.sh

echo "开始构建预览版PDF..."
echo "========================================"

# 设置文件名
MAIN_FILE="Main_Preview"
OUTPUT_PDF="${MAIN_FILE}.pdf"

# 清理之前的临时文件
echo "清理临时文件..."
rm -f ${MAIN_FILE}.aux ${MAIN_FILE}.bbl ${MAIN_FILE}.blg ${MAIN_FILE}.log ${MAIN_FILE}.out ${MAIN_FILE}.toc ${MAIN_FILE}.fdb_latexmk ${MAIN_FILE}.fls ${MAIN_FILE}.synctex.gz

# 第一次编译 - 生成目录和引用
echo "第一次编译 (生成目录和引用)..."
xelatex -interaction=nonstopmode ${MAIN_FILE}.tex
if [ $? -ne 0 ]; then
    echo "错误: 第一次编译失败"
    exit 1
fi

# 编译参考文献
echo "编译参考文献..."
bibtex ${MAIN_FILE}
if [ $? -ne 0 ]; then
    echo "警告: 参考文献编译可能有问题，继续..."
fi

# 第二次编译 - 处理参考文献
echo "第二次编译 (处理参考文献)..."
xelatex -interaction=nonstopmode ${MAIN_FILE}.tex
if [ $? -ne 0 ]; then
    echo "错误: 第二次编译失败"
    exit 1
fi

# 第三次编译 - 确保所有引用正确
echo "第三次编译 (确保引用正确)..."
xelatex -interaction=nonstopmode ${MAIN_FILE}.tex
if [ $? -ne 0 ]; then
    echo "错误: 第三次编译失败"
    exit 1
fi

# 检查输出文件
if [ -f "${OUTPUT_PDF}" ]; then
    echo "========================================"
    echo "预览版PDF构建成功!"
    echo "输出文件: ${OUTPUT_PDF}"
    echo "文件大小: $(du -h ${OUTPUT_PDF} | cut -f1)"
    echo "========================================"
    
    # 可选：打开PDF文件
    if command -v open >/dev/null 2>&1; then
        echo "是否要打开PDF文件? (y/n)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            open "${OUTPUT_PDF}"
        fi
    fi
else
    echo "错误: PDF文件生成失败"
    exit 1
fi

# 清理临时文件 (可选)
echo "清理临时文件..."
rm -f ${MAIN_FILE}.aux ${MAIN_FILE}.bbl ${MAIN_FILE}.blg ${MAIN_FILE}.log ${MAIN_FILE}.out ${MAIN_FILE}.toc ${MAIN_FILE}.fdb_latexmk ${MAIN_FILE}.fls ${MAIN_FILE}.synctex.gz

echo "构建完成!"
