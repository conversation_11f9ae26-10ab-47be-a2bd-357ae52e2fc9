@article{bengio2003neural,
  title={A neural probabilistic language model},
  author={<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, R{\'e}j<PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={Journal of Machine Learning Research},
  volume={3},
  pages={1137--1155},
  year={2003},
}

@inproceedings{mikolov2013distributed,
author = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
booktitle = {Advances in neural information processing systems},
editor = {<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>u and M, Welling},
pages = {3111--3119},
title = {Distributed representations of words and phrases and their compositionality},
address = {Red Hook, NY, USA},
publisher = {Curran Associates Inc.},
year = {2013},
}

@article{austin2021program,
  title={Program synthesis with large language models},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and others},
  journal={arXiv preprint arXiv:2108.07732},
  year={2021}
}
@article{chen2021evaluating,
  title={Evaluating large language models trained on code},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>ur<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and others},
  journal={ar<PERSON>iv preprint ar<PERSON>iv:2107.03374},
  year={2021}
}

@article{l<PERSON>2023<PERSON>nt<PERSON>ch,
  title={Agentbench: <PERSON>luating llms as agents},
  author={<PERSON>, <PERSON> and Yu, Ha<PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>fan and <PERSON>i, Xuanyu and Lai, Hanyu and Gu, Yu and Ding, Hangliang and Men, Kaiwen and Yang, Kejuan and others},
  journal={arXiv preprint arXiv:2308.03688},
  year={2023}
}

@article{sun2025autoeval,
  title={Autoeval: A practical framework for autonomous evaluation of mobile agents},
  author={Sun, Jiahui and Hua, Zhichao and Xia, Yubin},
  journal={arXiv preprint arXiv:2503.02403},
  year={2025}
}

@article{valmeekam2023planbench,
  title={Planbench: An extensible benchmark for evaluating large language models on planning and reasoning about change},
  author={Valmeekam, Karthik and Marquez, Matthew and Olmo, Alberto and Sreedharan, Sarath and Kambhampati, Subbarao},
  journal={Advances in Neural Information Processing Systems},
  volume={36},
  pages={38975--38987},
  year={2023}
}

@article{li2023api,
  title={Api-bank: A comprehensive benchmark for tool-augmented llms},
  author={Li, Minghao and Zhao, Yingxiu and Yu, Bowen and Song, Feifan and Li, Hangyu and Yu, Haiyang and Li, Zhoujun and Huang, Fei and Li, Yongbin},
  journal={arXiv preprint arXiv:2304.08244},
  year={2023}
}

@article{wang2023mint,
  title={Mint: Evaluating llms in multi-turn interaction with tools and language feedback},
  author={Wang, Xingyao and Wang, Zihan and Liu, Jiateng and Chen, Yangyi and Yuan, Lifan and Peng, Hao and Ji, Heng},
  journal={arXiv preprint arXiv:2309.10691},
  year={2023}
}
@article{wang2022execution,
  title={Execution-based evaluation for open-domain code generation},
  author={Wang, Zhiruo and Zhou, Shuyan and Fried, Daniel and Neubig, Graham},
  journal={arXiv preprint arXiv:2212.10481},
  year={2022}
}
@inproceedings{lai2023ds,
  title={DS-1000: A natural and reliable benchmark for data science code generation},
  author={Lai, Yuhang and Li, Chengxi and Wang, Yiming and Zhang, Tianyi and Zhong, Ruiqi and Zettlemoyer, Luke and Yih, Wen-tau and Fried, Daniel and Wang, Sida and Yu, Tao},
  booktitle={International Conference on Machine Learning},
  pages={18319--18345},
  year={2023},
  organization={PMLR}
}

@inproceedings{he2016residual,
  title={Deep Residual Learning for Image Recognition},
  author={He, Kaiming and Zhang, Xiangyu and Ren, Shaoqing and Sun, Jian},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={770--778},
  year={2016},
  organization={IEEE}
}

@online{gates2023age,
  author = {Gates, Bill},
  title = {The Age of AI Has Begun},
  year = {2023},
  month = {March},
  day = {22},
  url = {https://www.gatesnotes.com/the-age-of-ai-has-begun},
  urldate = {2024-12-19},
  note = {Blog post}
}

@online{karpathy2025software,
  author = {Karpathy, Andrej},
  title = {Software is Changing},
  year = {2025},
  url = {https://www.youtube.com/watch?v=LCEmiRjPEtQ},
  urldate = {2025-01-27},
  note = {YouTube video}
}

@book{russell2020artificial,
  title={Artificial Intelligence: A Modern Approach},
  author={Russell, Stuart J. and Norvig, Peter},
  edition={3},
  year={2020},
  publisher={Pearson},
  address={Hoboken, NJ},
  isbn={978-0134610993}
}

@inproceedings{yao2023react,
  title={React: Synergizing reasoning and acting in language models},
  author={Yao, Shunyu and Zhao, Jeffrey and Yu, Dian and Du, Nan and Shafran, Izhak and Narasimhan, Karthik and Cao, Yuan},
  booktitle={International Conference on Learning Representations (ICLR)},
  year={2023}
}

@article{wei2022chain,
  title={Chain-of-thought prompting elicits reasoning in large language models},
  author={Wei, Jason and Wang, Xuezhi and Schuurmans, Dale and Bosma, Maarten and Xia, Fei and Chi, Ed and Le, Quoc V and Zhou, Denny and others},
  journal={Advances in neural information processing systems},
  volume={35},
  pages={24824--24837},
  year={2022}
}

@article{yao2023tree,
  title={Tree of thoughts: Deliberate problem solving with large language models},
  author={Yao, Shunyu and Yu, Dian and Zhao, Jeffrey and Shafran, Izhak and Griffiths, Tom and Cao, Yuan and Narasimhan, Karthik},
  journal={Advances in neural information processing systems},
  volume={36},
  pages={11809--11822},
  year={2023}
}

@online{openai2025deep,
  author = {{OpenAI}},
  title = {Introducing Deep Research},
  year = {2025},
  month = {2},
  day = {2},
  url = {https://openai.com/index/introducing-deep-research/},
  urldate = {2025-02-02},
  note = {Blog post}
}

@online{openai2025operator,
  author = {{OpenAI}},
  title = {Introducing Operator},
  year = {2025},
  month = {1},
  day = {23},
  url = {https://openai.com/index/introducing-operator/},
  urldate = {2025-01-23},
  note = {Blog post}
}

@online{anthropic2024mcp,
  author = {{Anthropic}},
  title = {Introducing the Model Context Protocol},
  year = {2024},
  month = {November},
  day = {25},
  url = {https://www.anthropic.com/news/model-context-protocol},
  urldate = {2024-11-25},
  note = {Blog post}
}

@article{wooldridge1995agent,
  title={Agent theories, architectures, and languages: A survey},
  author={Wooldridge, Michael and Jennings, Nicholas R},
  journal={Intelligent agents},
  pages={1--39},
  year={1995},
  publisher={Springer}
}

@book{winston1992artificial,
  title={Artificial intelligence},
  author={Winston, Patrick Henry},
  year={1992},
  publisher={Addison-Wesley Longman Publishing Co., Inc.}
}

@book{wan2024llm,
  title={大语言模型应用指南：以ChatGPT为起点，从入门到精通的AI实践教程},
  author={万俊},
  year={2024},
  publisher={电子工业出版社},
  address={北京},
  isbn={978-7-121-47598-6}
}

@online{bloomberg2024openai,
  author = {{Bloomberg}},
  title = {OpenAI Scale Ranks Progress Toward 'Human-Level' Problem Solving},
  year = {2024},
  month = {July},
  day = {12},
  url = {https://www.bloomberg.com/news/articles/2024-07-11/openai-sets-levels-to-track-progress-toward-superintelligent-ai},
  urldate = {2024-07-12},
  note = {Bloomberg News article}
}

@inproceedings{morris2024position,
  title={Position: Levels of AGI for operationalizing progress on the path to AGI},
  author={Morris, Meredith Ringel and Sohl-Dickstein, Jascha and Fiedel, Noah and Warkentin, Tris and Dafoe, Allan and Faust, Aleksandra and Farabet, Clement and Legg, Shane},
  booktitle={Forty-first International Conference on Machine Learning},
  year={2024}
}

@online{ppc2025agents2,
  author = {{PPC}},
  title = {Agents2: New White Paper on Agent Technology},
  year = {2024},
  month = {January},
  url = {https://ppc.land/content/files/2025/01/Newwhitepaper_Agents2.pdf},
  urldate = {2024-09},
  note = {White paper on agent technology}
}

@online{openai2024agents,
  author = {{OpenAI}},
  title = {A Practical Guide to Building Agents},
  year = {2024},
  url = {https://cdn.openai.com/business-guides-and-resources/a-practical-guide-to-building-agents.pdf},
  note = {OpenAI business guide on building AI agents}
}

@online{anthropic2024effective,
  author = {Schluntz, Erik and Zhang, Barry},
  title = {Building effective agents},
  year = {2024},
  month = {December},
  day = {19},
  url = {https://www.anthropic.com/engineering/building-effective-agents},
  urldate = {2024-12-19},
  note = {Anthropic engineering blog post on building effective AI agents}
}

@misc{liu2025advanceschallengesfoundationagents,
      title={Advances and Challenges in Foundation Agents: From Brain-Inspired Intelligence to Evolutionary, Collaborative, and Safe Systems},
      author={Bang Liu and Xinfeng Li and Jiayi Zhang and Jinlin Wang and Tanjin He and Sirui Hong and Hongzhang Liu and Shaokun Zhang and Kaitao Song and Kunlun Zhu and Yuheng Cheng and Suyuchen Wang and Xiaoqiang Wang and Yuyu Luo and Haibo Jin and Peiyan Zhang and Ollie Liu and Jiaqi Chen and Huan Zhang and Zhaoyang Yu and Haochen Shi and Boyan Li and Dekun Wu and Fengwei Teng and Xiaojun Jia and Jiawei Xu and Jinyu Xiang and Yizhang Lin and Tianming Liu and Tongliang Liu and Yu Su and Huan Sun and Glen Berseth and Jianyun Nie and Ian Foster and Logan Ward and Qingyun Wu and Yu Gu and Mingchen Zhuge and Xiangru Tang and Haohan Wang and Jiaxuan You and Chi Wang and Jian Pei and Qiang Yang and Xiaoliang Qi and Chenglin Wu},
      year={2025},
      eprint={2504.01990},
      archivePrefix={arXiv},
      primaryClass={cs.AI},
      url={https://arxiv.org/abs/2504.01990}
}
