#!/bin/bash

echo "=== LaTeX表格扫描报告 ==="
echo "扫描时间: $(date)"
echo ""

for i in {1..12}; do
    file="chapters/chapter${i}.tex"
    if [ -f "$file" ]; then
        echo "=== 第${i}章 (chapter${i}.tex) ==="
        
        # 检查是否有表格
        table_count=$(grep -c "\\begin{table}" "$file" 2>/dev/null || echo "0")
        echo "表格数量: $table_count"
        
        if [ "$table_count" -gt 0 ]; then
            echo ""
            echo "表格详情:"
            
            # 找到所有表格的行号
            grep -n "\\begin{table}" "$file" | while read line; do
                line_num=$(echo "$line" | cut -d: -f1)
                echo "  表格开始行: $line_num"
                
                # 查找对应的caption和label
                sed -n "${line_num},/\\end{table}/p" "$file" | grep -n "\\caption\|\\label" | while read caption_line; do
                    echo "    $caption_line"
                done
                echo ""
            done
        else
            echo "无表格"
        fi
        echo ""
    else
        echo "=== 第${i}章 (chapter${i}.tex) ==="
        echo "文件不存在"
        echo ""
    fi
done
