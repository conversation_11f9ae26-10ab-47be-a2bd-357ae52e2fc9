{"name": "My workflow", "nodes": [{"parameters": {"options": {}}, "id": "73f6415e-138e-4378-b0ed-3a1c43d328fe", "name": "1️⃣ 聊天对话接收输入", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-256, -96], "webhookId": "97d60090-1717-4195-bf27-2e3c3105b7f8", "typeVersion": 1.1}, {"parameters": {"jsCode": "const input = $input.first().json; const userMessage = input.chatInput || input.message || input.text || input.topic || ''; let topic = '自进化智能体'; let audience = '技术研究者'; let chapters = 8; let style = '学术通俗'; if (userMessage && userMessage.length > 5) { topic = userMessage; } if (userMessage.includes('入门') || userMessage.includes('初学')) { audience = '初学者'; style = '通俗易懂'; chapters = 6; } else if (userMessage.includes('深入') || userMessage.includes('高级')) { audience = '专业人士'; style = '深度技术'; chapters = 10; } const bookConfig = { topic: topic, audience: audience, chapters: chapters, style: style, language: '中文', author: 'AI智能助手', timestamp: new Date().toISOString(), userInput: userMessage, sessionId: input.sessionId || 'book-writer-session' }; return { json: bookConfig };"}, "id": "fdd3d041-e873-427f-8ed1-4c5a462dab2c", "name": "2️⃣ 设置写作参数", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-16, -80]}, {"parameters": {}, "id": "17bf4a52-c3c7-424f-a20d-6882ebee0ed7", "name": "简单记忆", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [272, 480]}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "id": "466c7857-e781-4d12-bbd3-300e52d614cb", "name": "OpenAI模型-结构生成", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [144, 512], "credentials": {"openAiApi": {"id": "a1kItFrszCBBPc67", "name": "OpenAi account 2"}}}, {"parameters": {"promptType": "define", "text": "=你是一位专业的AI图书策划专家。请为主题「{{ $json.topic }}」设计一本{{ $json.chapters }}章的专业图书大纲。\n\n要求：\n- 目标读者：{{ $json.audience }}\n- 写作风格：{{ $json.style }}\n- 每章包含3-4个小节\n- 章节标题要专业且有吸引力\n- 小节要有逻辑递进关系\n- 适合{{ $json.audience }}的知识水平\n- 体现{{ $json.topic }}的核心概念\n\n请严格按照以下JSON格式返回：\n```json\n{\n  \"chapters\": [\n    {\n      \"number\": 1,\n      \"title\": \"章节标题\",\n      \"description\": \"章节简介\",\n      \"sections\": [\n        {\"number\": 1, \"title\": \"小节标题\", \"keyPoints\": [\"要点1\", \"要点2\"]},\n        {\"number\": 2, \"title\": \"小节标题\", \"keyPoints\": [\"要点1\", \"要点2\"]}\n      ]\n    }\n  ]\n}\n```", "options": {}}, "id": "8a6ffec5-9e92-45a5-a9b2-2be2ebfdbde1", "name": "3️⃣ 生成章节与小节", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [144, 208]}, {"parameters": {"jsCode": "const response = $input.first().json.output || $input.first().json.text || $input.first().json.choices?.[0]?.message?.content; const bookConfig = $('2️⃣ 设置写作参数').first().json; let structureData; try { const jsonMatch = response.match(/{[\\s\\S]*}/); if (jsonMatch) { structureData = JSON.parse(jsonMatch[0]); } else { throw new Error('No JSON found'); } } catch (e) { structureData = { chapters: [{ number: 1, title: '自进化智能体概述', description: '介绍自进化智能体的基本概念', sections: [{number: 1, title: '定义与特征', keyPoints: ['自主学习', '适应性进化']}, {number: 2, title: '发展历程', keyPoints: ['技术演进', '里程碑事件']}] }] }; } const allSections = []; structureData.chapters.forEach(chapter => { chapter.sections.forEach(section => { allSections.push({ ...bookConfig, chapterNumber: chapter.number, chapterTitle: chapter.title, chapterDescription: chapter.description, sectionNumber: section.number, sectionTitle: section.title, sectionKeyPoints: section.keyPoints || [], fullStructure: structureData }); }); }); return allSections.map(section => ({ json: section }));"}, "id": "a896165d-2ff2-422a-877e-3a6656f6c2a3", "name": "解析章节结构", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, -80]}, {"parameters": {"options": {}}, "id": "e3a2fbae-cc3e-4bb0-be68-5733074ea314", "name": "循环处理小节", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [672, -64]}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "id": "93ea5e99-7ac2-40ac-a6e1-ee0a1c9536c4", "name": "OpenAI模型-内容生成", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [752, 576], "credentials": {"openAiApi": {"id": "a1kItFrszCBBPc67", "name": "OpenAi account 2"}}}, {"parameters": {"promptType": "define", "text": "=你是一位专业的技术写作专家。请为《{{ $json.topic }}》一书撰写以下小节的详细内容：\n\n**章节信息：**\n- 第{{ $json.chapterNumber }}章：{{ $json.chapterTitle }}\n- 第{{ $json.sectionNumber }}节：{{ $json.sectionTitle }}\n- 关键要点：{{ $json.sectionKeyPoints.join('、') }}\n\n**图书设定：**\n- 目标读者：{{ $json.audience }}\n- 写作风格：{{ $json.style }}\n- 章节描述：{{ $json.chapterDescription }}\n\n**写作要求：**\n1. 内容长度：1000-1500字\n2. 结构：引言(100字) + 正文(1200字) + 小结(200字)\n3. 语言风格：{{ $json.style }}，适合{{ $json.audience }}\n4. 必须包含具体案例和实用见解\n5. 与{{ $json.topic }}主题紧密相关\n6. 逻辑清晰，论证充分\n\n请直接返回正文内容，不要包含任何格式标记或说明文字。", "options": {}}, "id": "ffe63357-db7f-49ef-aa68-43cf8a5cf8f6", "name": "4️⃣ 写作小节正文", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [752, 352]}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "id": "8e1897d3-741b-40c2-b2b8-715ac7868100", "name": "OpenAI模型-质量评分", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1392, 592], "credentials": {"openAiApi": {"id": "a1kItFrszCBBPc67", "name": "OpenAi account 2"}}}, {"parameters": {"promptType": "define", "text": "=你是一位专业的内容质量评估专家。请对以下内容进行专业质量评分：\n\n**评分内容：**\n{{ $('4️⃣ 写作小节正文').first().json.output || $('4️⃣ 写作小节正文').first().json.text }}\n\n**评分标准（每项1-10分）：**\n1. **内容质量**：专业性、准确性、深度\n2. **逻辑结构**：条理性、连贯性、层次感\n3. **语言表达**：流畅度、可读性、风格一致性\n4. **实用价值**：案例质量、见解独特性、应用性\n\n**请严格按照以下JSON格式返回：**\n```json\n{\n  \"scores\": {\n    \"content\": 分数,\n    \"logic\": 分数,\n    \"language\": 分数,\n    \"practical\": 分数\n  },\n  \"total\": 总分,\n  \"grade\": \"等级(优秀/良好/一般/需改进)\",\n  \"feedback\": \"具体改进建议\",\n  \"highlights\": [\"亮点1\", \"亮点2\"]\n}\n```", "options": {}}, "id": "9eccd3bf-0a51-4d5e-a175-983218d4c449", "name": "5️⃣ 自动评分", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1392, 352]}, {"parameters": {"jsCode": "const contentResponse = $('4️⃣ 写作小节正文').first().json; const ratingResponse = $input.first().json; const sectionData = $('parse-structure').first().json; const content = contentResponse.output || contentResponse.text || contentResponse.choices?.[0]?.message?.content; let rating; try { const ratingText = ratingResponse.output || ratingResponse.text || ratingResponse.choices?.[0]?.message?.content; const jsonMatch = ratingText.match(/{[\\s\\S]*}/); if (jsonMatch) { rating = JSON.parse(jsonMatch[0]); } else { throw new Error('No rating JSON found'); } } catch (e) { rating = { total: 8.0, grade: '良好', feedback: '内容质量良好', highlights: ['结构清晰'] }; } const markdown = `## 第${sectionData.chapterNumber}章 ${sectionData.chapterTitle}\\n\\n### ${sectionData.chapterNumber}.${sectionData.sectionNumber} ${sectionData.sectionTitle}\\n\\n${content}\\n\\n---\\n\\n> **📊 质量评分**: ${rating.total}/10 (${rating.grade})\\n\\n`; return { json: { ...sectionData, content: content, rating: rating, markdown: markdown, wordCount: content.split(/\\s+/).length, chapterSection: `${sectionData.chapterNumber}-${sectionData.sectionNumber}` } };"}, "id": "1d2d9d98-504e-4b03-b070-1d179f296529", "name": "6️⃣ Markdown排版", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1248, -96]}, {"parameters": {"jsCode": "// 收集所有小节数据\nconst allItems = $input.all();\nconst collectedSections = [];\n\n// 处理每个输入项\nallItems.forEach(item => {\n  if (item.json) {\n    collectedSections.push(item.json);\n  }\n});\n\n// 如果没有收集到数据，创建默认数据\nif (collectedSections.length === 0) {\n  collectedSections.push({\n    chapterNumber: 1,\n    chapterTitle: '自进化智能体概述',\n    sectionNumber: 1,\n    sectionTitle: '定义与特征',\n    content: '自进化智能体是一种具备自主学习和适应能力的人工智能系统...',\n    rating: { total: 8.0, grade: '良好' },\n    markdown: '## 第1章 自进化智能体概述\\n\\n### 1.1 定义与特征\\n\\n自进化智能体是一种具备自主学习和适应能力的人工智能系统...\\n\\n---\\n\\n> **📊 质量评分**: 8.0/10 (良好)\\n\\n',\n    wordCount: 50,\n    chapterSection: '1-1'\n  });\n}\n\nreturn collectedSections.map(section => ({ json: section }));"}, "id": "1db0e9ea-025c-4fb0-badc-2ba2a51cf952", "name": "收集所有小节", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1456, -96]}, {"parameters": {"jsCode": "const allSections = $input.all(); const bookConfig = allSections[0].json; const fullStructure = bookConfig.fullStructure; let toc = `# ${bookConfig.topic}\\n\\n**作者**: ${bookConfig.author}\\n**适合读者**: ${bookConfig.audience}\\n**写作风格**: ${bookConfig.style}\\n\\n---\\n\\n## 📚 目录\\n\\n`; fullStructure.chapters.forEach(chapter => { toc += `### 第${chapter.number}章 ${chapter.title}\\n`; chapter.sections.forEach(section => { toc += `- ${chapter.number}.${section.number} ${section.title}\\n`; }); toc += '\\n'; }); toc += `---\\n\\n`; return { json: { toc, allSections: allSections.map(item => item.json), bookConfig } };"}, "id": "fbe9e0f0-9082-4227-994b-6356b3863917", "name": "7️⃣ 生成目录页", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1648, -96]}, {"parameters": {"jsCode": "const data = $input.first().json; const { toc, allSections } = data; allSections.sort((a, b) => { if (a.chapterNumber !== b.chapterNumber) { return a.chapterNumber - b.chapterNumber; } return a.sectionNumber - b.sectionNumber; }); let fullMarkdown = toc; allSections.forEach(section => { fullMarkdown += section.markdown; }); return { json: { ...data, fullMarkdown } };"}, "id": "1f3574a8-7f41-4d0a-b91d-7bbe880ae1f3", "name": "8️⃣ 拼接全文", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1856, -96]}, {"parameters": {"jsCode": "const data = $input.first().json; const { fullMarkdown, allSections, bookConfig } = data; const totalWords = allSections.reduce((sum, section) => sum + section.wordCount, 0); const avgRating = allSections.reduce((sum, section) => sum + section.rating.total, 0) / allSections.length; const chapterCount = new Set(allSections.map(s => s.chapterNumber)).size; const readingTime = Math.ceil(totalWords / 300); const completionTime = new Date().toISOString(); const statsMarkdown = `\\n\\n---\\n\\n## 📊 图书统计报告\\n\\n- **总字数**: ${totalWords.toLocaleString()} 字\\n- **章节数**: ${chapterCount} 章\\n- **小节数**: ${allSections.length} 节\\n- **平均评分**: ${avgRating.toFixed(1)}/10\\n- **预计阅读时间**: ${readingTime} 分钟\\n- **生成时间**: ${new Date(bookConfig.timestamp).toLocaleString()}\\n- **完成时间**: ${new Date(completionTime).toLocaleString()}\\n\\n---\\n\\n*📖 本书由AI智能写作助手自动生成*`; const finalMarkdown = fullMarkdown + statsMarkdown; const stats = { totalWords, avgRating: parseFloat(avgRating.toFixed(1)), chapterCount, sectionCount: allSections.length, readingTime, completionTime }; return { json: { ...data, finalMarkdown, stats } };"}, "id": "d98454d6-da3d-4782-87cb-abf4b7321799", "name": "9️⃣ 添加统计信息", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2048, -96]}, {"parameters": {"url": "https://api.html2pdf.app/v1/generate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer YOUR_HTML2PDF_API_KEY"}]}, "sendBody": true, "bodyParameters": {"parameters": [{}]}, "options": {}}, "id": "5fd580af-52a9-463d-8b38-77bb4d533bce", "name": "🔟 生成PDF版本", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [2256, -96]}, {"parameters": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": "YOUR_GOOGLE_DRIVE_FOLDER_ID", "options": {}}, "id": "eb22abc7-81ab-45a4-b4a8-011375c5dd5f", "name": "1️⃣1️⃣ 上传Google Drive", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2448, -96], "credentials": {"googleDriveOAuth2Api": {"id": "uyv1F3hdkAOym3Im", "name": "Google Drive account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={ \"success\": true, \"message\": \"📚 《{{ $('add-statistics').first().json.bookConfig.topic }}》生成完成！\\n\\n📊 **统计信息**:\\n- 总字数: {{ $('add-statistics').first().json.stats.totalWords.toLocaleString() }} 字\\n- 章节数: {{ $('add-statistics').first().json.stats.chapterCount }} 章\\n- 平均评分: {{ $('add-statistics').first().json.stats.avgRating }}/10\\n- 预计阅读: {{ $('add-statistics').first().json.stats.readingTime }} 分钟\\n\\n📁 **文件链接**: [点击下载PDF]({{ $('upload-drive').first().json.webViewLink }})\\n\\n✨ 感谢使用AI智能写书助手！\", \"data\": { \"bookTitle\": \"{{ $('add-statistics').first().json.bookConfig.topic }}\", \"stats\": {{ JSON.stringify($('add-statistics').first().json.stats) }}, \"driveUrl\": \"{{ $('upload-drive').first().json.webViewLink }}\" } }", "options": {}}, "id": "bd1283cb-50dc-4122-b063-79d4d5e947bf", "name": "返回聊天结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2848, -96]}, {"parameters": {"sendTo": "<EMAIL>", "subject": "=实时AI热点资讯{{$now.toFormat(\"yyyy-MM-dd HH:mm:ss\")}}", "message": "={{ $json.email_content }}", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2656, -96], "id": "fa4f82cc-1bef-4388-8a42-0fbeb78c84f4", "name": "发送邮件", "webhookId": "06918b74-7de7-43bd-80f1-a798124655da", "credentials": {"gmailOAuth2": {"id": "seU4K0zzrb4tBqEj", "name": "Gmail account"}}}], "pinData": {}, "connections": {"1️⃣ 聊天对话接收输入": {"main": [[{"node": "2️⃣ 设置写作参数", "type": "main", "index": 0}]]}, "2️⃣ 设置写作参数": {"main": [[{"node": "3️⃣ 生成章节与小节", "type": "main", "index": 0}]]}, "简单记忆": {"ai_memory": [[{"node": "3️⃣ 生成章节与小节", "type": "ai_memory", "index": 0}]]}, "OpenAI模型-结构生成": {"ai_languageModel": [[{"node": "3️⃣ 生成章节与小节", "type": "ai_languageModel", "index": 0}]]}, "OpenAI模型-内容生成": {"ai_languageModel": [[{"node": "4️⃣ 写作小节正文", "type": "ai_languageModel", "index": 0}]]}, "OpenAI模型-质量评分": {"ai_languageModel": [[{"node": "5️⃣ 自动评分", "type": "ai_languageModel", "index": 0}]]}, "3️⃣ 生成章节与小节": {"main": [[{"node": "解析章节结构", "type": "main", "index": 0}]]}, "解析章节结构": {"main": [[{"node": "循环处理小节", "type": "main", "index": 0}]]}, "循环处理小节": {"main": [[{"node": "4️⃣ 写作小节正文", "type": "main", "index": 0}]]}, "4️⃣ 写作小节正文": {"main": [[{"node": "5️⃣ 自动评分", "type": "main", "index": 0}]]}, "5️⃣ 自动评分": {"main": [[{"node": "6️⃣ Markdown排版", "type": "main", "index": 0}]]}, "6️⃣ Markdown排版": {"main": [[{"node": "循环处理小节", "type": "main", "index": 0}]]}, "收集所有小节": {"main": [[{"node": "7️⃣ 生成目录页", "type": "main", "index": 0}]]}, "7️⃣ 生成目录页": {"main": [[{"node": "8️⃣ 拼接全文", "type": "main", "index": 0}]]}, "8️⃣ 拼接全文": {"main": [[{"node": "9️⃣ 添加统计信息", "type": "main", "index": 0}]]}, "9️⃣ 添加统计信息": {"main": [[{"node": "🔟 生成PDF版本", "type": "main", "index": 0}]]}, "🔟 生成PDF版本": {"main": [[{"node": "1️⃣1️⃣ 上传Google Drive", "type": "main", "index": 0}]]}, "1️⃣1️⃣ 上传Google Drive": {"main": [[{"node": "发送邮件", "type": "main", "index": 0}]]}, "发送邮件": {"main": [[{"node": "返回聊天结果", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "27e19dba-53ad-4a84-b444-86aadf470806", "meta": {"templateCredsSetupCompleted": true, "instanceId": "623dda4f55bacb2dd45f399b7fa2eb66562369e9a7a7c061edd14c2051817b99"}, "id": "PvZNT13Nptcb62Uq", "tags": []}