{"name": "智能写书Agent - 完整工作流", "nodes": [{"parameters": {"httpMethod": "POST", "path": "book-writer", "responseMode": "responseNode", "options": {}}, "id": "webhook-start", "name": "1️⃣ 对话方式接收输入", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 200], "webhookId": "book-writer-start"}, {"parameters": {"jsCode": "// 解析用户输入并设置默认值\nconst input = $input.first().json.body || $input.first().json;\nconst bookConfig = {\n  topic: input.topic || input.主题 || '人工智能与未来社会',\n  audience: input.audience || input.读者群体 || '普通大众',\n  chapters: parseInt(input.chapters || input.章节数 || 8),\n  style: input.style || input.写作风格 || '通俗易懂',\n  language: input.language || input.语言 || '中文',\n  author: input.author || input.作者 || 'AI助手',\n  timestamp: new Date().toISOString()\n};\nreturn { json: bookConfig };"}, "id": "set-variables", "name": "2️⃣ 设置写作参数", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 200]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-4", "text": "=作为专业的图书策划师，请为主题「{{ $json.topic }}」设计一本面向「{{ $json.audience }}」的{{ $json.chapters }}章图书大纲。\n\n要求：\n1. 每章标题要有吸引力和逻辑性\n2. 章节间要有递进关系\n3. 适合{{ $json.style }}的写作风格\n4. 用{{ $json.language }}回答\n\n请以JSON格式返回，格式如下：\n{\n  \"chapters\": [\n    {\"number\": 1, \"title\": \"章节标题\", \"description\": \"章节简介\"},\n    ...\n  ]\n}"}, "id": "generate-structure", "name": "3️⃣ 生成图书结构", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [600, 200]}, {"parameters": {"jsCode": "// 解析章节结构并准备循环处理\nconst response = $input.first().json.choices[0].message.content;\nlet chaptersData;\ntry {\n  chaptersData = JSON.parse(response);\n} catch (e) {\n  // 如果JSON解析失败，尝试提取章节信息\n  const chapters = [];\n  const lines = response.split('\\n');\n  let chapterNum = 1;\n  lines.forEach(line => {\n    if (line.includes('章') || line.includes('Chapter')) {\n      chapters.push({\n        number: chapterNum++,\n        title: line.trim(),\n        description: '待生成详细内容'\n      });\n    }\n  });\n  chaptersData = { chapters };\n}\n\nconst bookConfig = $('set-variables').first().json;\nconst result = chaptersData.chapters.map(chapter => ({\n  ...bookConfig,\n  currentChapter: chapter,\n  chapterNumber: chapter.number,\n  chapterTitle: chapter.title,\n  chapterDescription: chapter.description\n}));\n\nreturn result.map(item => ({ json: item }));"}, "id": "parse-chapters", "name": "解析章节数据", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 200]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-4", "text": "=为第{{ $json.chapterNumber }}章「{{ $json.chapterTitle }}」设计详细的小节大纲。\n\n章节描述：{{ $json.chapterDescription }}\n图书主题：{{ $json.topic }}\n目标读者：{{ $json.audience }}\n写作风格：{{ $json.style }}\n\n要求：\n1. 设计3-5个小节\n2. 每个小节要有明确的学习目标\n3. 小节间要有逻辑递进关系\n4. 适合{{ $json.audience }}的理解水平\n\n请以JSON格式返回：\n{\n  \"sections\": [\n    {\"number\": 1, \"title\": \"小节标题\", \"objective\": \"学习目标\", \"keyPoints\": [\"要点1\", \"要点2\"]},\n    ...\n  ]\n}"}, "id": "generate-sections", "name": "4️⃣ 生成小节大纲", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1000, 200]}, {"parameters": {"jsCode": "// 解析小节数据并准备内容生成\nconst response = $input.first().json.choices[0].message.content;\nlet sectionsData;\ntry {\n  sectionsData = JSON.parse(response);\n} catch (e) {\n  // 简单解析备用方案\n  const sections = [];\n  for (let i = 1; i <= 4; i++) {\n    sections.push({\n      number: i,\n      title: `第${i}节 核心内容`,\n      objective: '掌握核心概念',\n      keyPoints: ['重点1', '重点2']\n    });\n  }\n  sectionsData = { sections };\n}\n\nconst chapterData = $input.first().json;\nconst result = sectionsData.sections.map(section => ({\n  ...chapterData,\n  currentSection: section,\n  sectionNumber: section.number,\n  sectionTitle: section.title,\n  sectionObjective: section.objective,\n  sectionKeyPoints: section.keyPoints\n}));\n\nreturn result.map(item => ({ json: item }));"}, "id": "parse-sections", "name": "解析小节数据", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 200]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-4", "text": "=请为第{{ $json.chapterNumber }}章第{{ $json.sectionNumber }}节撰写详细内容。\n\n章节：{{ $json.chapterTitle }}\n小节：{{ $json.sectionTitle }}\n学习目标：{{ $json.sectionObjective }}\n关键要点：{{ $json.sectionKeyPoints }}\n\n图书信息：\n- 主题：{{ $json.topic }}\n- 读者：{{ $json.audience }}\n- 风格：{{ $json.style }}\n\n写作要求：\n1. 内容长度：800-1200字\n2. 结构清晰，有引言、正文、小结\n3. 语言{{ $json.style }}，适合{{ $json.audience }}\n4. 包含具体例子和实用建议\n5. 用{{ $json.language }}撰写\n\n请直接返回正文内容，不要包含格式标记。"}, "id": "generate-content", "name": "5️⃣ 生成小节内容", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1400, 200]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-3.5-turbo", "text": "=请对以下内容进行质量评分（1-10分）：\n\n内容：{{ $json.choices[0].message.content }}\n\n评分维度：\n1. 语言表达（流畅度、准确性）\n2. 逻辑结构（条理性、连贯性）\n3. 内容深度（专业性、实用性）\n4. 可读性（易懂程度、吸引力）\n\n请以JSON格式返回：\n{\n  \"scores\": {\n    \"language\": 分数,\n    \"logic\": 分数,\n    \"depth\": 分数,\n    \"readability\": 分数\n  },\n  \"total\": 总分,\n  \"feedback\": \"改进建议\"\n}"}, "id": "rate-content", "name": "6️⃣ 内容质量评分", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1600, 200]}, {"parameters": {"jsCode": "// 格式化为Markdown并添加评分信息\nconst contentResponse = $('generate-content').first().json;\nconst ratingResponse = $input.first().json;\nconst sectionData = $('parse-sections').first().json;\n\nconst content = contentResponse.choices[0].message.content;\nlet rating;\ntry {\n  rating = JSON.parse(ratingResponse.choices[0].message.content);\n} catch (e) {\n  rating = { total: 8, feedback: '内容质量良好' };\n}\n\n// 生成Markdown格式\nconst markdown = `## 第${sectionData.chapterNumber}章 ${sectionData.chapterTitle}\n\n### ${sectionData.sectionNumber}. ${sectionData.sectionTitle}\n\n${content}\n\n---\n\n*质量评分: ${rating.total}/10 | ${rating.feedback}*\n\n`;\n\nreturn {\n  json: {\n    ...sectionData,\n    content: content,\n    rating: rating,\n    markdown: markdown,\n    wordCount: content.split(/\\s+/).length\n  }\n};"}, "id": "format-markdown", "name": "7️⃣ Markdown格式化", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1800, 200]}, {"parameters": {"mode": "mergeByIndex", "options": {}}, "id": "aggregate-content", "name": "8️⃣ 聚合所有内容", "type": "n8n-nodes-base.itemLists", "typeVersion": 3, "position": [2000, 200]}, {"parameters": {"jsCode": "// 生成目录页\nconst items = $input.all();\nconst bookConfig = items[0].json;\n\n// 按章节分组\nconst chapterGroups = {};\nitems.forEach(item => {\n  const chapterNum = item.json.chapterNumber;\n  if (!chapterGroups[chapterNum]) {\n    chapterGroups[chapterNum] = {\n      title: item.json.chapterTitle,\n      sections: []\n    };\n  }\n  chapterGroups[chapterNum].sections.push({\n    number: item.json.sectionNumber,\n    title: item.json.sectionTitle\n  });\n});\n\n// 生成目录Markdown\nlet toc = `# ${bookConfig.topic}\\n\\n*作者：${bookConfig.author}*\\n*适合读者：${bookConfig.audience}*\\n*写作风格：${bookConfig.style}*\\n\\n---\\n\\n## 目录\\n\\n`;\n\nObject.keys(chapterGroups).sort((a, b) => parseInt(a) - parseInt(b)).forEach(chapterNum => {\n  const chapter = chapterGroups[chapterNum];\n  toc += `### 第${chapterNum}章 ${chapter.title}\\n`;\n  chapter.sections.forEach(section => {\n    const anchor = `第${chapterNum}章-${section.number}-${section.title}`.replace(/[\\s\\W]/g, '-');\n    toc += `- [${section.number}. ${section.title}](#${anchor})\\n`;\n  });\n  toc += '\\n';\n});\n\ntoc += '---\\n\\n';\n\nreturn { json: { toc, bookConfig, allContent: items.map(item => item.json) } };"}, "id": "generate-toc", "name": "9️⃣ 生成目录页", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2200, 200]}, {"parameters": {"jsCode": "// 拼接全文Markdown\nconst data = $input.first().json;\nconst { toc, allContent } = data;\n\n// 按章节和小节排序\nallContent.sort((a, b) => {\n  if (a.chapterNumber !== b.chapterNumber) {\n    return a.chapterNumber - b.chapterNumber;\n  }\n  return a.sectionNumber - b.sectionNumber;\n});\n\n// 拼接所有内容\nlet fullMarkdown = toc;\nallContent.forEach(item => {\n  fullMarkdown += item.markdown;\n});\n\nreturn { json: { ...data, fullMarkdown, totalSections: allContent.length } };"}, "id": "merge-content", "name": "🔟 拼接全文", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2400, 200]}, {"parameters": {"jsCode": "// 添加统计信息\nconst data = $input.first().json;\nconst { fullMarkdown, allContent, bookConfig } = data;\n\n// 计算统计信息\nconst totalWords = allContent.reduce((sum, item) => sum + item.wordCount, 0);\nconst avgRating = allContent.reduce((sum, item) => sum + item.rating.total, 0) / allContent.length;\nconst readingTime = Math.ceil(totalWords / 300); // 假设每分钟300字\nconst completionTime = new Date().toISOString();\n\n// 添加统计信息到文档末尾\nconst statsMarkdown = `\\n\\n---\\n\\n## 📊 图书统计信息\\n\\n- **总字数**: ${totalWords.toLocaleString()} 字\\n- **章节数**: ${Object.keys(allContent.reduce((acc, item) => { acc[item.chapterNumber] = true; return acc; }, {})).length} 章\\n- **小节数**: ${allContent.length} 节\\n- **平均质量评分**: ${avgRating.toFixed(1)}/10\\n- **预计阅读时间**: ${readingTime} 分钟\\n- **生成时间**: ${new Date(bookConfig.timestamp).toLocaleString()}\\n- **完成时间**: ${new Date(completionTime).toLocaleString()}\\n\\n---\\n\\n*本书由AI智能写作助手生成*`;\n\nconst finalMarkdown = fullMarkdown + statsMarkdown;\n\nreturn { json: { ...data, finalMarkdown, stats: { totalWords, avgRating, readingTime, completionTime } } };"}, "id": "add-stats", "name": "1️⃣1️⃣ 添加统计信息", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2600, 200]}, {"parameters": {"url": "https://api.html2pdf.app/v1/generate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "html", "value": "=<html><head><meta charset='utf-8'><style>body{font-family:Arial,sans-serif;line-height:1.6;margin:40px;} h1,h2,h3{color:#333;} pre{background:#f4f4f4;padding:10px;border-radius:5px;}</style></head><body><pre>{{ $json.finalMarkdown }}</pre></body></html>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "your-html2pdf-api-key"}]}}, "id": "generate-pdf", "name": "1️⃣2️⃣ 生成PDF", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [2800, 200]}, {"parameters": {"authentication": "oAuth2", "resource": "file", "operation": "upload", "binaryData": true, "fileName": "={{ $('add-stats').first().json.bookConfig.topic.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_') }}_{{ new Date().toISOString().split('T')[0] }}.pdf", "folderId": "your-google-drive-folder-id", "options": {"description": "=AI生成图书：{{ $('add-stats').first().json.bookConfig.topic }}"}}, "id": "upload-storage", "name": "1️⃣3️⃣ 上传存储", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [3000, 200]}, {"parameters": {"resource": "message", "operation": "post", "channel": "#book-notifications", "text": "=📚 新书生成完成！\\n\\n**书名**: {{ $('add-stats').first().json.bookConfig.topic }}\\n**作者**: {{ $('add-stats').first().json.bookConfig.author }}\\n**字数**: {{ $('add-stats').first().json.stats.totalWords.toLocaleString() }} 字\\n**章节**: {{ Object.keys($('add-stats').first().json.allContent.reduce((acc, item) => { acc[item.chapterNumber] = true; return acc; }, {})).length }} 章\\n**质量评分**: {{ $('add-stats').first().json.stats.avgRating.toFixed(1) }}/10\\n**预计阅读**: {{ $('add-stats').first().json.stats.readingTime }} 分钟\\n\\n📁 文件已上传到Google Drive\\n📧 详细信息请查看邮件", "otherOptions": {}}, "id": "notify-completion", "name": "1️⃣4️⃣ 通知完成", "type": "n8n-nodes-base.slack", "typeVersion": 2, "position": [3200, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={ \"success\": true, \"message\": \"图书生成完成\", \"bookTitle\": \"{{ $('add-stats').first().json.bookConfig.topic }}\", \"stats\": {{ JSON.stringify($('add-stats').first().json.stats) }}, \"downloadUrl\": \"{{ $('upload-storage').first().json.webViewLink }}\" }"}, "id": "webhook-response", "name": "返回结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [3400, 200]}], "connections": {"1️⃣ 对话方式接收输入": {"main": [[{"node": "2️⃣ 设置写作参数", "type": "main", "index": 0}]]}, "2️⃣ 设置写作参数": {"main": [[{"node": "3️⃣ 生成图书结构", "type": "main", "index": 0}]]}, "3️⃣ 生成图书结构": {"main": [[{"node": "解析章节数据", "type": "main", "index": 0}]]}, "解析章节数据": {"main": [[{"node": "4️⃣ 生成小节大纲", "type": "main", "index": 0}]]}, "4️⃣ 生成小节大纲": {"main": [[{"node": "解析小节数据", "type": "main", "index": 0}]]}, "解析小节数据": {"main": [[{"node": "5️⃣ 生成小节内容", "type": "main", "index": 0}]]}, "5️⃣ 生成小节内容": {"main": [[{"node": "6️⃣ 内容质量评分", "type": "main", "index": 0}]]}, "6️⃣ 内容质量评分": {"main": [[{"node": "7️⃣ Markdown格式化", "type": "main", "index": 0}]]}, "7️⃣ Markdown格式化": {"main": [[{"node": "8️⃣ 聚合所有内容", "type": "main", "index": 0}]]}, "8️⃣ 聚合所有内容": {"main": [[{"node": "9️⃣ 生成目录页", "type": "main", "index": 0}]]}, "9️⃣ 生成目录页": {"main": [[{"node": "🔟 拼接全文", "type": "main", "index": 0}]]}, "🔟 拼接全文": {"main": [[{"node": "1️⃣1️⃣ 添加统计信息", "type": "main", "index": 0}]]}, "1️⃣1️⃣ 添加统计信息": {"main": [[{"node": "1️⃣2️⃣ 生成PDF", "type": "main", "index": 0}]]}, "1️⃣2️⃣ 生成PDF": {"main": [[{"node": "1️⃣3️⃣ 上传存储", "type": "main", "index": 0}]]}, "1️⃣3️⃣ 上传存储": {"main": [[{"node": "1️⃣4️⃣ 通知完成", "type": "main", "index": 0}]]}, "1️⃣4️⃣ 通知完成": {"main": [[{"node": "webhook-response", "type": "main", "index": 0}]]}}}