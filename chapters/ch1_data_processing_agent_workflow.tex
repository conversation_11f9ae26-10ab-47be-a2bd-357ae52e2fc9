\begin{figure}[h!t]
\centering
\begin{tikzpicture}[
    node distance=0.6cm and 0.8cm,
    every node/.style={font=\tiny},
    datasource/.style={rectangle, draw=green!60, fill=green!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt, inner sep=2pt},
    integration/.style={rectangle, draw=orange!60, fill=orange!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt, inner sep=2pt},
    catalog/.style={rectangle, draw=cyan!60, fill=cyan!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt, inner sep=2pt},
    dataarch/.style={rectangle, draw=red!60, fill=red!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt, inner sep=2pt},
    service/.style={rectangle, draw=blue!60, fill=blue!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt, inner sep=2pt},
    development/.style={rectangle, draw=blue!60, fill=blue!15, thick, minimum width=1.8cm, minimum height=0.6cm, align=center, rounded corners=1pt, inner sep=2pt},
    arrow/.style={thick,->,>=stealth},
    bigbox/.style={rectangle, draw=red!80, thick, rounded corners=2pt, inner sep=0.2cm}
]

% 左侧：数据源 - 适度增加左边距
\node (obs_data) [datasource] at (-7.0,4) {数据源\\OBS\\出行数据};

% 数据集成
\node (cdm_process) [integration] at (-4.8,4) {数据集成\\CDM处理};

% 数据目录
\node (hive_catalog) [catalog] at (-2.6,4) {数据目录\\HIVE\\元数据};

% 中央数据架构大框 - 保持中央位置
\draw [bigbox] (-0.5,1.0) rectangle (4.5,6.5);
\node [above] at (2.0,6.5) {\textbf{\tiny 数据架构}};

% 数据架构内部 - 顶层 - 保持内部间距
\node (sdh_layer) [dataarch] at (0.2,6.0) {SDH层\\数据源头};
\node (dwh_layer) [dataarch] at (2.0,6.0) {DWH层\\数据仓库};
\node (data_service_layer) [dataarch] at (3.8,6.0) {数据服务层\\查询接口};

% 数据架构内部 - 中层
\node (dwh_detail) [dataarch] at (2.0,4.5) {DWH层\\HIVE\\数据建模\\质量治理};

% 数据架构内部 - 底层
\node (data_dev) [dataarch] at (0.2,3.0) {数据开发\\建模治理};
\node (dmm_layer) [dataarch] at (3.2,3.0) {DMM\\工具集\\开发工具};

% 右侧：数据服务 - 适度增加右侧间距
\node (data_service_r) [service] at (5.8,5.5) {数据服务\\HIVE};
\node (data_analysis) [service] at (5.8,4.5) {数据分析\\HIVE};
\node (data_mining) [service] at (5.8,3.5) {数据挖掘\\HIVE};
\node (api_service) [service] at (5.8,2.5) {开放API\\数据接口};

% 最右侧：用户 - 适度增加右边距
\node (user) [service] at (7.8,4.0) {用户\\数据科学家\\业务分析师};

% 底部：数据开发流程 - 调整水平居中
\node (dev_title) [development] at (2.0,0.5) {\textbf{\tiny 数据开发流程}\\HIVE};

% 开发流程详细步骤 - 适度扩展水平分布
\node (data_input) [development] at (-2.5,-0.5) {数据输入\\人工输入};
\node (data_process) [development] at (-0.3,-0.5) {数据处理\\清洗转换};
\node (data_model) [development] at (2.0,-0.5) {数据建模\\维度建模};
\node (data_output) [development] at (4.3,-0.5) {数据输出\\导出发布};
\node (data_monitor) [development] at (6.5,-0.5) {数据监控\\质量监控};

% 主要数据流连接线
\draw [arrow] (obs_data) -- (cdm_process);
\draw [arrow] (cdm_process) -- (hive_catalog);
\draw [arrow] (hive_catalog) -- (sdh_layer);
\draw [arrow] (sdh_layer) -- (dwh_layer);
\draw [arrow] (dwh_layer) -- (data_service_layer);
\draw [arrow] (data_service_layer) -- (data_service_r);
\draw [arrow] (data_service_r) -- (user);

% 数据架构内部连接
\draw [arrow] (dwh_layer) -- (dwh_detail);
\draw [arrow] (dwh_detail) -- (data_dev);
\draw [arrow] (dwh_detail) -- (dmm_layer);

% 右侧服务连接
\draw [arrow] (data_service_r) -- (data_analysis);
\draw [arrow] (data_analysis) -- (data_mining);
\draw [arrow] (data_mining) -- (api_service);

% 数据架构到开发流程的连接
\draw [arrow] (data_dev) -- (dev_title);
\draw [arrow] (dev_title) -- (data_input);

% 开发流程内部连接
\draw [arrow] (data_input) -- (data_process);
\draw [arrow] (data_process) -- (data_model);
\draw [arrow] (data_model) -- (data_output);
\draw [arrow] (data_output) -- (data_monitor);

% 反馈连接
\draw [arrow, dashed] (data_monitor) to[bend left=45] (data_dev);

\end{tikzpicture}
\caption{数据处理智能体的任务流程与环境图}
\label{fig:ch1_data_proc}
\end{figure}
