\begin{figure}[h!t]
\centering
\begin{tikzpicture}[
    node distance=0.4cm and 0.6cm,
    every node/.style={font=\tiny},
    datasource/.style={rectangle, draw=green!60, fill=green!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt},
    integration/.style={rectangle, draw=orange!60, fill=orange!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt},
    catalog/.style={rectangle, draw=cyan!60, fill=cyan!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt},
    dataarch/.style={rectangle, draw=red!60, fill=red!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt},
    service/.style={rectangle, draw=blue!60, fill=blue!15, thick, minimum width=1.4cm, minimum height=0.7cm, align=center, rounded corners=1pt},
    development/.style={rectangle, draw=blue!60, fill=blue!15, thick, minimum width=1.8cm, minimum height=0.6cm, align=center, rounded corners=1pt},
    arrow/.style={thick,->,>=stealth},
    bigbox/.style={rectangle, draw=red!80, thick, rounded corners=2pt, inner sep=0.15cm}
]

% 左侧：数据源 - 进一步缩小坐标
\node (obs_data) [datasource] at (-5.5,3) {数据源\\OBS\\出行数据表\\2017\_Yellow\_Taxi\_Trip\_Data.csv};

% 数据集成
\node (cdm_process) [integration] at (-3.8,3) {数据集成\\CDM处理\\数据清洗\\数据标准化};

% 数据目录
\node (hive_catalog) [catalog] at (-2.1,3) {数据目录\\HIVE\\元数据管理\\数据血缘分析};

% 中央数据架构大框 - 缩小尺寸
\draw [bigbox] (-1.2,0.8) rectangle (3.6,4.8);
\node [above] at (1.2,4.8) {\textbf{\tiny 数据架构}};

% 数据架构内部 - 顶层 - 缩小坐标
\node (sdh_layer) [dataarch] at (-0.6,4.4) {SDH层\\数据源头层\\Big\_Data\_File.data};
\node (dwh_layer) [dataarch] at (1.2,4.4) {DWH层\\数据仓库层\\Big\_Data\_File.data};
\node (data_service_layer) [dataarch] at (3,4.4) {数据服务层\\数据服务接口\\数据查询服务};

% 数据架构内部 - 中层
\node (dwh_detail) [dataarch] at (1.2,3.4) {DWH层\\HIVE\\数据仓库\\数据建模\\数据质量\\数据治理\\数据安全\\数据生命周期管理};

% 数据架构内部 - 底层
\node (data_dev) [dataarch] at (-0.6,2.2) {数据开发\\数据开发\\数据建模\\数据质量\\数据治理\\数据安全\\数据生命周期管理};
\node (dmm_layer) [dataarch] at (2.4,2.2) {DMM\\工具集\\数据建模工具\\数据质量工具\\数据治理工具\\数据安全工具\\数据生命周期管理工具};

% 右侧：数据服务 - 缩小坐标
\node (data_service_r) [service] at (4.8,3.8) {数据服务\\HIVE\\数据查询服务};
\node (data_analysis) [service] at (4.8,3.2) {数据分析\\HIVE\\数据分析服务};
\node (data_mining) [service] at (4.8,2.6) {数据挖掘\\HIVE\\数据挖掘服务};
\node (api_service) [service] at (4.8,2.0) {开放API\\数据API\\服务API};

% 最右侧：用户 - 进一步缩小坐标
\node (user) [service] at (6.2,2.8) {用户\\数据科学家\\业务分析师};

% 底部：数据开发流程 - 缩小坐标
\node (dev_title) [development] at (1.2,0.2) {\textbf{\tiny 数据开发流程}\\HIVE};

% 开发流程详细步骤 - 缩小坐标和尺寸
\node (data_input) [development] at (-1.8,-0.8) {数据输入\\人工输入\\Big\_Data\_File.data};
\node (data_process) [development] at (-0.3,-0.8) {数据处理\\数据清洗\\数据转换\\数据验证};
\node (data_model) [development] at (1.2,-0.8) {数据建模\\维度建模\\事实表建模};
\node (data_output) [development] at (2.8,-0.8) {数据输出\\数据导出\\数据发布};
\node (data_monitor) [development] at (4.5,-0.8) {数据监控\\数据质量监控\\数据血缘监控};

% 主要数据流连接线
\draw [arrow] (obs_data) -- (cdm_process);
\draw [arrow] (cdm_process) -- (hive_catalog);
\draw [arrow] (hive_catalog) -- (sdh_layer);
\draw [arrow] (sdh_layer) -- (dwh_layer);
\draw [arrow] (dwh_layer) -- (data_service_layer);
\draw [arrow] (data_service_layer) -- (data_service_r);
\draw [arrow] (data_service_r) -- (user);

% 数据架构内部连接
\draw [arrow] (dwh_layer) -- (dwh_detail);
\draw [arrow] (dwh_detail) -- (data_dev);
\draw [arrow] (dwh_detail) -- (dmm_layer);

% 右侧服务连接
\draw [arrow] (data_service_r) -- (data_analysis);
\draw [arrow] (data_analysis) -- (data_mining);
\draw [arrow] (data_mining) -- (api_service);

% 数据架构到开发流程的连接
\draw [arrow] (data_dev) -- (dev_title);
\draw [arrow] (dev_title) -- (data_input);

% 开发流程内部连接
\draw [arrow] (data_input) -- (data_process);
\draw [arrow] (data_process) -- (data_model);
\draw [arrow] (data_model) -- (data_output);
\draw [arrow] (data_output) -- (data_monitor);

% 反馈连接
\draw [arrow, dashed] (data_monitor) to[bend left=45] (data_dev);

\end{tikzpicture}
\caption{数据处理智能体的任务流程与环境图}
\label{fig:ch1_data_proc}
\end{figure}
