\begin{figure}[h!t]
\centering
\begin{tikzpicture}[
    node distance=1.2cm and 1.5cm,
    every node/.style={font=\small},
    task/.style={rectangle, draw=blue!60, fill=blue!10, thick, minimum width=3.5cm, minimum height=1.2cm, align=center, rounded corners=3pt},
    env/.style={rectangle, draw=green!60, fill=green!10, thick, minimum width=2.8cm, minimum height=1cm, align=center, rounded corners=3pt},
    tool/.style={rectangle, draw=orange!60, fill=orange!10, thick, minimum width=2.5cm, minimum height=0.8cm, align=center, rounded corners=3pt},
    process/.style={rectangle, draw=red!60, fill=red!10, thick, minimum width=3cm, minimum height=1cm, align=center, rounded corners=3pt},
    arrow/.style={thick,->,>=stealth},
    group/.style={rectangle, draw=gray!60, thick, rounded corners=5pt, inner sep=0.3cm}
]

% 任务定义 (顶部)
\node (task_def) [task] at (0,6) {挑战性任务\\数据可视化};

% 复杂环境 (左侧)
\node (env_title) [env, below=1.5cm of task_def, xshift=-4cm] {复杂环境};
\node (markdown) [env, below=0.5cm of env_title] {Markdown文档};
\node (yaml) [env, below=0.5cm of markdown] {YAML配置};
\node (database) [env, below=0.5cm of yaml] {数据库};
\node (tables) [env, below=0.5cm of database] {表格文件};
\node (data_folder) [env, below=0.5cm of tables] {数据文件夹};

% 工具集 (右侧)
\node (tools_title) [tool, below=1.5cm of task_def, xshift=4cm] {可用工具};
\node (sql_tool) [tool, below=0.5cm of tools_title] {SQL查询};
\node (python_tool) [tool, below=0.5cm of sql_tool] {Python编程};
\node (bash_tool) [tool, below=0.5cm of python_tool] {Bash命令};

% 智能体核心流程 (中央)
\node (agent) [process, below=2cm of task_def] {数据处理智能体};

% 思考-动作-观察循环
\node (think) [process, below=1cm of agent] {思考\\分析需求};
\node (action) [process, below=1cm of think] {动作\\执行命令};
\node (observe) [process, below=1cm of action] {观察\\检查结果};

% 输出结果
\node (result) [task, below=1cm of observe] {任务完成\\生成图表};

% 环境反馈框
\node (feedback_box) [group, fit=(think)(action)(observe), label=right:反馈循环] {};

% 连接箭头
\draw [arrow] (task_def) -- (agent);
\draw [arrow] (agent) -- (think);
\draw [arrow] (think) -- (action);
\draw [arrow] (action) -- (observe);
\draw [arrow] (observe) -- (result);

% 循环箭头
\draw [arrow, bend left=45] (observe.east) to (think.east);

% 环境到智能体的连接
\draw [arrow, dashed] (markdown.east) -- (agent.west);
\draw [arrow, dashed] (yaml.east) -- (agent.west);
\draw [arrow, dashed] (database.east) -- (agent.west);
\draw [arrow, dashed] (tables.east) -- (agent.west);
\draw [arrow, dashed] (data_folder.east) -- (agent.west);

% 工具到动作的连接
\draw [arrow, dashed] (sql_tool.west) -- (action.east);
\draw [arrow, dashed] (python_tool.west) -- (action.east);
\draw [arrow, dashed] (bash_tool.west) -- (action.east);

% 标注
\node [above=0.2cm of feedback_box, font=\footnotesize] {持续反馈循环};

\end{tikzpicture}
\caption{数据处理智能体的任务流程与环境图}
\label{fig:ch1_data_proc}
\end{figure}
