\chapter{智能体基础概念}

% 本章核心主要阐释
% 1.1 智能体的概念，讨论什么是智能体，什么不是智能体，智能体与LLM有什么区别，智能体与workflows的区别；
% 1.2 【理论】智能体的发展历史，解答一些大家比较疑惑的问题，为什么李飞飞说2025年会是智能体元年；
% 1.3 【实践】智能体 = 
%               LM（什么时候使用对自己需求的model，根据task complexity, latency, and cost，或者使用多个models；从小到大选择模型，小的能work的绝对不选更困难的）+
%               Tools + 
%               编排层


\section{背景与定义}

\subsection{背景}

近年来，“智能体”（Agent）这一概念逐渐成为人工智能（Artificial Intelligence，AI）领域广泛关注的热点。我们不禁要问：为什么“智能体”突然变得如此重要？它与我们日常所理解的人工智能有什么区别？为什么值得我们单独探讨和深入研究？从计算机科学的角度来看，人工智能的核心目标是构建能够展现智能行为的“代理”（智能体）。但“智能行为”究竟意味着什么？智能体能否完全复制人类的智能？如果不能，那么实现部分人类智能的智能体是否具备实际价值？明确这些问题，将帮助我们深入理解智能体为何逐步占据人工智能研究与产业实践的核心位置。

人们对智能体抱有不同的期望。有一种观点认为，理想的智能体应当能够完全重现人类智能行为的各个方面，但这种目标过于宏大且不现实。更广为接受的观点是，即便目前无法实现完整的人类智能，构建能够展现部分人类智能特征的智能体也极具现实价值。从人工智能研究的本质来看，其核心即是研究智能体——这些智能体能够主动感知环境，进行自主决策并采取行动，以达成既定目标，如图\ref{fig:ch1_agent_interact_with_env}所示。从这个角度看，智能体远不止于一种软件程序，而更接近具备自主“观察—思考—行动”能力的智能个体。

\begin{figure}[h!t]\centering
  \includegraphics[width=.9\linewidth]{imgs/ch1/agent_interact_with_env.png}
  \caption{智能体与环境交互示意图}
  \label{fig:ch1_agent_interact_with_env}
\end{figure}

虽然智能体的概念早在几十年前便已提出，但主流的人工智能研究直到上世纪八十年代中后期才开始真正关注并投入到智能体的构建与应用当中。这一阶段的智能体研究快速兴起，逐步渗透到了软件工程、数据通信、并发系统、机器人技术以及分布式人工智能等多个领域。然而，长期以来，由于计算能力和数据规模的限制，智能体技术始终未能成为人工智能研究的主流方向，而更多地以一个支线或分支的形式存在于研究领域的边缘。

近年来，以ChatGPT、Claude、Gemini、DeepSeek、Qwen、GLM、Kimi等为代表的大语言模型（Large Language Model，LLM）迅速崛起，标志着人工智能技术的一次重大突破。这些模型首次实现了对自然语言的深刻理解与灵活生成能力，从而直接推动了智能体技术的快速发展，使智能体真正具备了自主理解用户意图、规划行动路径并完成任务的能力。业界普遍认为，我们即将迎来真正能理解自然语言、准确感知用户需求并主动完成任务的“个人AI助手时代”。著名人工智能专家李飞飞和Andrej Karpathy\footnote{本书对广为人知的人物使用其通行的中文译名，如比尔·盖茨、李飞飞；对于尚无统一中文译名的 AI 领域科学家与工程师，则保留其英文原名，以避免歧义。}更进一步指出，2025年将成为智能体元年，开启属于智能体技术蓬勃发展的新十年。

智能体技术在科研领域的迅速崛起，离不开一系列关键理论与方法论的突破。最早提出的代表性理论之一是思维链（Chain-of-Thought，CoT），它通过引导语言模型逐步展现其推理过程，显著提高了模型在数学计算、逻辑推理、问答任务中的表现。然而，CoT的推理模式仍然局限于线性单一路径，难以有效处理需要多步规划、回溯修正或并行探索的复杂任务。为克服这些局限性，研究者进一步提出了思维树（Tree-of-Thoughts，ToT）框架。与CoT不同，ToT将模型的推理路径从单一线性扩展为树状结构，使模型可以同时探索、评估并回溯多个可能路径，大大提升了模型在复杂决策任务中的战略选择与全局搜索能力。

相较于CoT和ToT聚焦于纯粹的“思考”与推理过程，ReAct框架进一步实现了“思考”与“行动”的深度融合。ReAct首次提出了“思考–行动–感知–再思考”的闭环智能体机制，这使得智能体不仅可以通过清晰可解释的推理链条进行决策，而且能够实时感知外部环境、动态调整行动路径，例如主动查询API或操作网页，以此体现出更强的鲁棒性与更高的人类可理解性。从方法论的演进来看，CoT奠定了语言模型推理的基础，ToT则推动了模型在复杂环境下的深度探索能力，而ReAct实现了推理与行动的融合，标志着智能体从理论上的纸面智能向真正可操作、可实践的智能体迈出了关键一步。这三大框架共同构成了当前智能体研究的重要理论基石，为构建具有规划、感知、执行和反思能力的高级人工智能提供了坚实的方法论基础。

随着理论和基础架构的不断完善，一批面向实际应用场景的智能体产品开始落地，展现出从“生成信息”向“执行任务”的根本跃迁。OpenAI推出的Operator，便是一款能够主动操作网页的智能代理，已经在电商下单、票务处理、在线客服等场景中投入使用，具备感知界面、解析目标、执行交互的能力。而中国公司Manus所开发的通用AI代理系统，则在金融咨询、人才招聘、商务差旅等复杂任务中展现了从任务规划、工具调用到多轮总结与交付的全流程智能，甚至支持用户通过自然语言调度多个工具协作完成跨系统任务。这类产品标志着AI从“回答问题的助手”进化为“完成事务的代理人”，预示着智能体范式正逐步进入可操作、可部署、可协作的新阶段。它不仅改变了人机交互的模式，也为企业级应用和智能自动化带来了切实可行的路径。

随着智能体系统逐渐进入企业级应用，现有 AI 模型的结构性局限性逐渐显现，尤其体现在实时访问外部数据和工具的能力不足上。尽管这些模型在语言理解与推理方面展现出强大的性能，但由于缺乏标准化和有效的对外部数据源和工具的实时连接机制，模型在执行涉及动态数据的任务时往往处于“信息孤岛”的状态，无法高效、准确地完成实时任务。“信息孤岛”具体表现为数据的孤立与碎片化，模型难以有效整合和利用外部实时信息。例如，在检索增强生成（Retrieval-Augmented Generation，RAG）过程中，一些常见的粗糙实现方案是直接将未经处理的网页数据原样输入模型上下文，这种方法造成上下文冗长、结构混乱，严重影响模型的理解能力与响应质量。

针对上述问题，Anthropic提出了模型上下文协议（Model Context Protocol，MCP）。MCP定义了结构化的数据请求与响应格式，提供统一的标准接口协议，从根本上解决了数据孤立和信息碎片化问题。MCP允许开发者便捷地接入Google Drive、Slack、GitHub、Postgres等多种系统，使AI模型能够动态获取上下文信息、调用工具和执行任务。此外，MCP还内建权限控制与安全机制，以确保AI对敏感数据访问的安全性与合规性。这种标准化接口机制有效降低了AI系统集成的复杂性与成本，推动了智能体系统的模块化和可组合化发展，为构建大规模、高互操作性的智能体生态提供了重要基础。

与此同时，围绕智能体构建和调度的开发框架（如图\ref{fig:ch1_agent_frameworks_stars}）也迅速发展，为开发者提供了更清晰、更高效的开发路径。代表性的框架包括Google的智能体 Development Kit（ADK），它以模块化和灵活性著称，支持多智能体系统的快速搭建和部署，简化了复杂任务的实现流程；微软的AutoGen框架则采用异步消息传递和事件驱动架构，增强了多智能体间的协作能力和系统的可扩展性，内置调试与监控工具，适合构建复杂的代理工作流；此外，LangChain生态中的LangGraph则通过图结构管理智能体交互，内置状态管理、循环工作流和人机协作机制，极大提升了复杂非线性任务的处理能力和系统的鲁棒性。这些框架与技术的结合，推动了智能代理技术在实际应用中的广泛落地和创新发展。

\begin{figure}[h!t]\centering
  \includegraphics[width=.9\linewidth]{imgs/ch1/github_agents_2025.pdf}
  \caption{主流智能体开发框架GitHub Star数量对比（2025年6月）}
  \label{fig:ch1_agent_frameworks_stars}
\end{figure}

综上所述，智能体已不再是语言模型能力的一层包装，而正在演化为具备目标驱动、工具调用、自主感知与状态记忆等关键能力的完整智能体系统。从基础推理范式（如CoT、ToT、ReAct）的演进，到通信协议（如MCP）的建立，再到工程工具链（如Autogen、ADK、LangGraph）的快速发展，智能体 技术正逐步形成一套集理论、协议、框架、执行于一体的系统化范式。它改变的不只是模型的使用方式，更重塑了我们与AI互动的基本方式：从“问答接口”到“行动主体”，从“任务指令”到“目标代理”。

\subsection{定义}

智能体一词在英语中本来的含义就是“代理人”或“经纪人”，是指那些受人委托代替他人办事或执行任务的人或实体。例如，我们旅行时会委托“旅行代理”（Travel Agent）帮忙规划和预订行程；买房时会请“地产经纪人”（Real Estate Agent）替我们寻找合适的房源、协调价格、处理手续。这些“智能体”的关键特征，就是能主动代替我们完成任务或处理事务，而不是简单地提供建议或回答问题。

在计算机领域，我们称为智能体的软件系统或机器人也具备类似特征——它们能主动代替用户感知环境变化、做出决策并调用工具完成任务，从而显著降低人类的操作成本和负担。因此，从本质上讲，计算机领域的智能体延续了英文原意，即以人为中心、主动替人完成任务的“智能代理”，并且能够自主感知环境、进行决策并执行行动的智能实体，广泛存在于实际应用中，例如自动驾驶汽车、智能语音助手和工业机器人等场景。通过这些实例，我们可以初步把握智能体的核心特征：自主性与智能性。所以AI 智能体可以看作是一种具备高度自主性的人工智能系统，这个系统具有引导结果走向目标的能力。它能够在较长时间内独立运行，通过调用外部工具完成复杂任务，并能基于环境反馈持续学习与优化，从而不断增强其任务完成能力与系统稳定性。智能体的行为模式由三个基本能力支撑：

\begin{itemize}
  \item 感知：智能体通过传感器获取外部环境的数据。例如，自动驾驶汽车利用雷达、激光雷达（LiDAR）和摄像头实时监测道路状况、行人位置及障碍物信息。这些设备赋予智能体对环境的敏锐洞察，类似于人类的感官系统。
  \item 思考：智能体基于感知数据进行分析并制定决策。以自动驾驶为例，系统会综合路况、导航目标和交通规则，判断是否需要加速、减速或转向。这一过程体现了智能体的逻辑推理能力，类似于人类在复杂情境中的思维活动。
  \item 行动：智能体通过执行器将决策转化为具体操作。在自动驾驶场景中，车辆通过控制油门、刹车和方向盘实现加速、停车或转弯等动作。这种能力使智能体能够主动影响环境，完成预定任务。

\end{itemize}

如图\ref{fig:ch1_agent_interact_with_env}所示，智能体（智能体）是指任何可以通过传感器感知其环境，控制中心进行思考决策，最后由执行器立即执行相应动作。根据，整个流程可简要表述为：
\[
  \text{Perceive（感知）} \;\rightarrow\; \text{Reason（推理）} \;\rightarrow\; \text{Act（行动）}\,.
\]

尽管智能体架构几十年来在概念上并无根本性改变，但在具体实现细节上却经历了重要的演化。其中最显著的变化发生在推理决策阶段。早期人工智能系统中，推理往往基于明确的符号逻辑与规则推导，这种方式虽逻辑严谨却受制于知识规模与环境适应性的局限。随着数据驱动的方法兴起，以大语言模型为代表的概率式推理逐渐取代了传统符号主义。LLM通过海量数据的训练，能够有效应对模糊且复杂的现实问题，大幅增强了智能体在开放世界中的鲁棒性与灵活性。

与此同时，感知与行动环节也随之演进。从早期简单有限的感知设备，到如今支持多模态实时数据处理的复杂传感系统，智能体的感知能力显著增强。行动环节则从单一预定义的规则动作，转变为可调用多种工具与API的灵活动作集，使智能体的执行能力得以显著提升。这种整体演进体现了人工智能系统从确定性、静态的传统设计，走向更加动态、弹性的现代实现路径。

在前面的小节中，我们通过日常的实例与抽象的流程描述，初步掌握了智能体的直观概念：一种能够主动感知环境、推理决策并自主执行行动的实体。为进一步将这一直观认知推向严谨的理论范畴，我们必须回顾经典的学术定义，并对智能体的本质特征做出更加清晰的界定。

根据经典论文《\textit{Intelligent Agents: Theories, Architectures, and Languages}》，Wooldridge等人提出了智能体的三个核心特征：反应性（reactivity）、主动性（pro-activeness）与社会性（social ability）。这三个维度成为后续人工智能领域普遍接受的智能体定义标准。

\begin{itemize}
  \item 反应性：是指智能体能够实时感知并响应环境的变化。这与传统的软件程序形成鲜明对比，传统程序通常只按固定的指令或触发事件被动执行任务，缺乏对外界变化的主动应对能力。举例来说，自动驾驶汽车在感知到突然出现的障碍物时，会立即作出减速或转向反应；智能客服机器人则能根据用户输入的实时需求快速作出回答或引导。这种实时反馈机制是智能体能与动态环境有效交互的基础特征。
  \item 主动性：意味着智能体不仅仅被动地响应环境变化，更具备主动追求特定目标并采取预见性行动的能力。与被动系统如工作流（workflow）不同，后者只会机械地按照预定义的路径前进，一旦遇到未预设情况便难以处理。主动性的智能体则能自发生成和规划目标，预测环境的未来状态并提前进行干预。比如，智慧能源管理系统可预见电网负荷高峰，提前规划能源储备与调度策略，主动保障系统稳定性和效率。
  \item 社会性：则强调智能体能够与其他智能体（包括人类）进行有效交互和沟通。具备社会性的智能体不仅可以传递信息，还能理解和影响其他智能体的行为。智能体系统常常需要跨系统、跨团队合作完成任务，例如多无人机编队协作、智能客服与用户互动、协作机器人（Cobots）与人类并肩作业等。这种协作能力是智能体技术能够广泛落地应用的重要前提。
\end{itemize}

进一步地，Russell与Norvig 从功能角度指出智能体本质上是一个映射过程（智能体函数）：根据历史感知序列决定当前行动，形式化为：
\[
f: P^* \rightarrow A
\]
其中，$P^*$表示所有历史感知序列集合，$A$表示可能行动集合。与之对应的是智能体程序，即实际实现这种理想函数的算法或软件。

综合来看，学术定义与数学抽象共同明确了智能体概念的内涵和边界，为后续章节探讨智能体实现与应用提供了严谨的理论基石。下一小节，我们将进一步探讨智能体与传统软件、工作流（Workflow）及大型语言模型（LLM）的区别，以清晰界定其独特应用价值。

\subsection{智能体与模型，LLM，workflow，传统程序}

尽管智能体概念在人工智能领域已被广泛讨论，但与传统程序、工作流（Workflow）以及大语言模型（LLM）的界限却经常容易混淆。常见的疑问包括：智能体与传统程序在控制流和执行逻辑方面有何本质差异？Workflow和智能体都能实现一定程度的自动化，但在功能和应用场景上又如何明确区分？大语言模型具备强大的语言理解与生成能力，将其与工具接口相结合后，是否能成为完整的智能体？明确这些问题，不仅有助于更严谨地定义智能体概念，也能在实际工程应用中为技术选型提供清晰的理论依据。

为了更深入理解智能体与传统程序、Workflow及大语言模型（LLM）的本质区别，有必要借鉴Andrej Karpathy在2025年YC演讲中提出的“三代软件”框架，并重新明确各个概念的定位与本质差异。

\begin{figure}[h!t]\centering
  \includegraphics[width=\textwidth]{imgs/ch1/software_evolve.png}
  \caption{软件范式的演进：从 \textbf{Software 1.0} 的显式规则代码（如：\texttt{if distance < 10 then brake()}），发展到 \textbf{Software 2.0} 中由训练数据和模型参数驱动的神经网络（如：\texttt{y = model.predict(image)}），最终演进到 \textbf{Software 3.0}，通过自然语言指令实现控制（如：\texttt{'There is a car ahead, slow down.'}）。}
  \label{fig:software_evolution}
\end{figure}


根据 Karpathy 的阐述，软件技术经历了如下三个显著阶段，如图\ref{fig:software_evolution}所示：

\textbf{Software 1.0} 以显式源代码为核心，通过工程师手动编写规则逻辑实现明确的任务，典型案例如 Linux 内核、ERP 系统。这类软件即传统意义上的\textbf{传统程序}，由明确的逻辑与固定的执行路径组成。例如，一个简单的天气提醒程序：
\begin{verbatim}
if temperature < 0:
    print("天气寒冷，注意保暖！")
else:
    print("天气舒适，适合出门。")
\end{verbatim}
程序的输入（温度）和输出（提醒内容）明确固定，但当情况变复杂或变化频繁时，其维护成本指数级增长。

\textbf{Software 2.0} 是数据驱动时代的产物，其核心是神经网络模型的参数权重，不再依赖人工明确编写的规则，而是基于大量数据学习特征，典型案例如 AlexNet 和自动驾驶视觉识别系统。这种软件即我们所称的\textbf{模型}（Model）。以自动驾驶系统为例，通过海量图像数据训练的深度学习模型（如 ResNet），能实时识别路上的行人、车辆，具有较强的泛化能力。然而，这些模型本质上只是预测工具，本身并不会主动调用外部工具或主动执行任务。

\textbf{Software 3.0} 时代的核心特征则以大语言模型（LLM）为代表，通过自然语言指令（Prompt）与用户交互，例如 GPT-4o、Claude 和 Gemini。这类技术统称为\textbf{大型语言模型}（LLM），是一种高度灵活的语言理解与生成引擎。例如，当你询问 GPT“Python 如何排序一个列表？”时，它立即给出：
\begin{verbatim}
# 使用 sort 方法：
my_list = [5, 2, 9, 1]
my_list.sort()
#my_list 现在是：[1, 2, 5, 9]
\end{verbatim}
这种交互方式的优势在于用户无需显式定义规则，但纯粹的 LLM 并不具备主动感知环境或调用工具闭环执行任务的能力。

进一步地，为了更清晰地区分上述三代软件技术与\textbf{Workflow}及\textbf{智能体}概念，我们需要明确它们的功能定位：

\textbf{Workflow} 指的是预定义的工作流程，它可以整合传统程序、模型甚至 LLM，以确定的方式串联起一系列自动化任务，具有明确的步骤和条件。例如，一个自动发布文章的流程：
\[
\text{撰写文章} \rightarrow \text{检查拼写} \rightarrow \text{管理员审核} \rightarrow \text{定时发布网站} \rightarrow \text{邮件通知订阅用户}
\]
每个步骤都是确定且可控的，虽然可以有条件分支，但路径和处理方式都是预定义的。这类流程广泛应用于企业自动化办公（如 RPA）或论文撰写中的 BibTeX 文献自动引用流程。

相比之下，\textbf{智能体} 则体现了一种高级的智能工作流方式，它在 Software 3.0 基础上实现了主动感知、动态决策和工具调度，形成了完整的“感知—思考—行动”闭环。例如，当用户在 ChatGPT 网页版询问：“如何用 Python 自动抓取网页数据并生成报告？”时，ChatGPT 不仅会给出说明，还能主动检索最新接口、调用工具编写和执行代码，并自动整理形成可执行的报告。另一个典型的实体例子则是智能扫地机器人，它能实时感知环境、规划最佳清扫路径、避开障碍并适时充电。这种动态、主动的闭环交互正是智能体区别于传统程序、模型、LLM 以及 Workflow 的核心特征。一般我们打开的Gemini、ChatGPT、DeepSeek的界面是智能体，而只有其API才是LLM，LLM本质上是软件，是智能体的大脑，是核心，LLM是缸中之脑。

综上所述，以上五种概念及其与 Software 1.0–3.0 的对应关系可简明归纳为表\ref{tab:software_comparison}：

\begin{table}[htbp]
\centering
\caption{不同软件概念与 Software 1.0–3.0 的对应关系}
\label{tab:software_comparison}
\renewcommand{\arraystretch}{1.5} % 行距更美观
\begin{tabularx}{\textwidth}{|>{\centering\arraybackslash}X|>{\centering\arraybackslash}X|>{\centering\arraybackslash}X|}
\hline
\textbf{类型} & \textbf{核心特征} & \textbf{典型实例} \\ \hline
传统程序（Software 1.0） & 人工明确规则、被动执行 & 天气提醒脚本 \\ \hline
模型（Software 2.0） & 数据驱动预测，无主动调用工具能力 & 自动驾驶视觉识别模型 \\ \hline
LLM（Software 3.0） & 自然语言交互，无主动工具调用与环境感知 & GPT-4o直接回答编程问题的界面（纯文本对话） \\ \hline
Workflow & 预定义确定的自动化任务流 & 自动发布文章流程 \\ \hline
智能体 & 主动感知环境，动态决策与闭环工具调度 & ChatGPT 网页版（具有联网搜索、插件调用、代码解释执行等功能），智能扫地机器人 \\ \hline
\end{tabularx}
\end{table}

\subsection{智能体的发展路线}

在深入讨论智能体技术之前，我们首先要明确一个关键概念——AGI（通用人工智能）。AGI指具备与人类同等甚至超越人类智能的系统，能够表现出所有正常人类所具备的智能行为。当前，Google、OpenAI、DeepSeek等公司的长期目标均聚焦于实现AGI，但智能体与AGI之间的关系仍需谨慎梳理：智能体是朝向AGI演进的具体实践路径之一，但二者不能简单等同。

OpenAI近期内部提出了AGI发展的五个阶段，这一框架清晰地反映了智能体技术的发展趋势。

\textbf{第一阶段：对话模型。}在这一阶段，AI主要以自然语言交互为核心技术，实现语言理解与生成。具体技术以Transformer架构为基础，代表系统如OpenAI的ChatGPT、DeepSeek-V3。这类模型极大提升了人类信息获取和沟通的效率，但本质上仍为被动工具，尚不具备自主决策能力。

\textbf{第二阶段：推理模型。}这一阶段的关键技术为推理能力的提升，以Chain-of-Thought（CoT）、Tree-of-Thoughts（ToT）为代表，使语言模型具备更强大的逻辑推理与问题解决能力。OpenAI的O1/O3系列、DeepSeek-R1属于此类模型，其推理能力已达博士级人类水平，在科研、医疗、法律等领域展示出巨大的潜力，同时也带来了对高技能岗位的潜在影响。

\textbf{第三阶段：智能体。}智能体阶段的AI核心特征在于主动性，能够自主感知、规划并执行任务，实现持续运行与环境交互。此阶段的代表技术为ReAct架构，通过与外部工具的紧密结合，实现真正意义上的闭环执行能力。OpenAI于2025年初推出的Operator模型正是这一阶段的典型代表。智能体的出现预示着工作模式的重大转变，同时也可能带来隐私、安全及伦理等方面的新挑战。

\textbf{第四阶段：创新者。}创新者阶段的AI具备独立创新能力，能够自主提出新理论、创造新产品。这阶段的实现将依靠高级生成模型及更具自主性的学习算法推动AI进行理论探索和原创设计。当前尚无实际落地案例，技术实现难度巨大，这一阶段的到来也意味着人类在创造力领域的主导地位将面临严峻挑战。

\textbf{第五阶段：组织者。}AGI发展的终极阶段即组织者阶段，AI可独立完成整个组织的所有任务，实现真正的全自主运行。这一阶段技术要求极高，需要多智能体系统的高效协作、自适应架构及全面自主性机制的完善。目前，该阶段仍处于理论假设阶段，未有任何系统实现。

此外，Google DeepMind也在2023年提出了自己的AGI发展框架，将AI的能力划分为“普通人”、“有竞争力”、“专家”和“超人类”等多个层次。这一分级模型借鉴了自动驾驶技术的分级理念，通过评估AI能力在各领域中的表现程度，更细致地反映了从有限任务辅助到完全自主解决复杂问题的进化路径。这种结构化的能力分级为我们理解AGI提供了另一种有价值的视角。

通过OpenAI和Google DeepMind的AGI发展框架，我们清晰看到智能体技术正一步步朝向更高阶段的AGI目标推进。但必须认识到，当前的智能体技术更多地强调实际应用与具体技术实现，而AGI则是更为宏观和长期的愿景目标。智能体在迈向AGI的过程中，每一步都伴随着重要的技术进步与严峻的社会考验，这种谨慎而清晰的理解有助于更准确地定义技术路线与长远愿景之间的边界。

\section{智能体系统 = 大模型 + 工具 + 编排层}
\subsection{智能体系统的概念与架构}

前文中我们使用的是“智能体”，但在本节特意采用“智能体系统（agentic system）”这一术语，以强调我们所讨论的不仅是单个智能体，而是一套完整且具备系统性架构的智能体系。本书整体上将围绕智能体展开，但在探讨具体的技术实现与复杂的系统构造时，明确强调这种“系统性”是非常必要的。因此，我们在本节专门介绍智能体系统，阐明其架构及各核心组件之间的关系。

近年来，随着以ChatGPT、Claude、Gemini为代表的大型语言模型快速发展，人工智能的应用范式也迎来了重要的转变：从传统的被动响应转向主动的智能体系统。智能体系统是一种能够主动感知环境变化、独立决策并自主执行任务的智能系统。

业界对智能体系统的定义虽存在差异，但基本架构大体一致。例如，Google《智能体白皮书》定义的智能体系统为“大模型+工具+编排层”，而OpenAI则定义为“大模型+工具+指令层”。值得注意的是，OpenAI的定义中所使用的术语“指令”，通常更容易被解读为简单的提示语工程，这种表述方式可能在一定程度上弱化了编排层所具有的复杂性与重要性。因此，本书更倾向于Google的定义，采用“编排层”这一术语，更明确地强调了这一层次在实际系统中的核心地位。具体来说，智能体系统由三个核心组件构成：

\textbf{大模型：}大模型是智能体系统的核心“大脑”，具备语言理解、逻辑推理和动态决策能力。大模型的引入，使智能体系统能够更精准地理解用户需求，有效进行任务规划和决策。当下先进的大语言模型之所以能够让用户仅使用自然语言即可描述需求，是因为这些模型掌握了语言的深层结构和模式，能将模糊的自然语言转化成清晰的执行指令或具体的编程代码。这种强大的翻译和理解能力，使得非专业用户也能高效实现技术方案，而无需具备传统编程技能，正如当下流行的观点所言：“现在最热门的编程语言是自然语言”，大幅降低了开发者的技术门槛。

尽管大模型在处理语言和推理方面表现优异，但仍存在模型幻觉，即在未明确数据支持时可能生成错误或虚假的信息。此外，大模型受限于输入上下文长度，难以处理过长的信息，并且在实时数据访问和动态响应方面也存在一定局限。这意味着大模型无法完全取代人工的所有决策，智能体系统仍需明确的边界和人工监督。
% TODO：解释下一章要说明如何选择模型

\textbf{工具：}工具赋予智能体系统与外界环境交互的能力，包括数据检索、生成报告、数据库操作、API调用等。工具的使用使智能体具备主动感知环境变化并采取相应行动的能力。

工具自身的设计质量直接影响智能体系统的运行效率。如果工具的设计过于复杂或调用流程烦琐，或存在安全风险，可能导致智能体的任务执行效率低下甚至出错。因此，工具设计应尽可能精简明确且安全可靠。

\textbf{编排层：}编排层负责协调“大模型”和“工具”之间的交互，确定任务执行的顺序和方式。它确保感知（通过工具实现）、推理（通过大模型完成）与行动（通过工具实现）之间的有效协作。编排层设计复杂，涉及大量的动态任务管理、错误处理及异常情况管理。如果设计不够完善，可能导致系统运行的不稳定性和执行效率的下降，尤其是在多智能体系统的应用场景下。

此外，为了更全面地理解智能体系统，我们还需要关注以下几个对智能体系统至关重要的组件：

\textbf{感知系统：}负责从外部环境中获取信息并进行预处理，是智能体系统的“眼睛”和“耳朵”。感知系统的作用包括实时感知环境变化，将原始数据转化为可被大模型处理的信息。其局限在于可能受限于感知硬件性能、数据质量及感知算法的准确性。

\textbf{工具系统：}赋予智能体系统与外界进行交互的能力，包括数据库访问、API调用、执行动作等，类似人类的手和工具。其局限在于工具本身的设计复杂性、安全性以及可靠性，过于复杂或安全性差的工具可能会降低智能体系统的整体性能。

\textbf{规划决策系统：}智能体系统的“思考引擎”，负责基于感知信息、记忆信息和目标，进行任务规划和决策，确定下一步行动。大模型极大地强化了规划决策系统，使用户无需精通传统计算机编程语言，即可通过自然语言清晰地表达需求和规划任务。但需要指出的是，当前大模型的能力仍然存在局限，例如在缺乏明确数据支持时容易产生幻觉，这意味着规划决策系统仍需要明确的监督机制。

\textbf{记忆系统：}负责存储、检索并更新长期和短期的知识、经验与技能，帮助智能体在长期的任务执行和连续互动中保持稳定性和一致性。记忆系统的局限性包括存储和检索效率问题，以及如何选择性地遗忘无关或过时信息。

\textbf{反思系统：}使智能体能够对过去的决策和行动进行自我评估和反思，以识别问题和优化策略。这种反思机制能显著提高智能体系统的自适应能力和自主学习能力，但同时也增加了系统的复杂性和计算成本。

\textbf{动作系统：}实现智能体的实际行动，是智能体系统“手脚”的延伸。动作系统决定智能体如何物理或虚拟地执行任务。动作系统的局限在于硬件条件、动作执行的准确性和可靠性，以及如何平衡探索和利用的策略。

值得注意的是，上述组件与本节提出的“大模型+工具+编排层”并非相互独立，而是紧密协作的整体。例如，感知和记忆系统为大模型提供丰富的数据基础，而规划决策系统和反思系统则深度依赖于大模型和工具系统的支持，动作系统更是规划决策的具体实现。上述各个组件的详细内容，将分别在第二章至第七章中展开讨论，以便读者系统地理解和掌握智能体系统的各个重要方面。

\subsection{智能体系统的分类：从简单反应到复杂认知}

为了系统地理解智能体的多样性，我们可以将其按照其内部状态的复杂性、决策机制以及与环境交互的方式进行分类。这种分类有助于我们把握不同类型智能体的能力边界、适用场景及其设计上的核心挑战。本节将详细探讨从简单的反应式智能体到具备复杂认知能力的智能体的各种类型。

首先，我们按照应用场景和交互方式，将智能体大致划分为对话式、任务型、具身智能和决策型四大类。这种“主流分类”一方面便于在具体项目中快速选型——当你需要做客户服务，就自然而然地落到对话式智能体；当你要在工业现场实现自动化，就关注具身智能智能体。但这种以“它们做什么”或“它们在哪儿跑”来分类的方式，虽然直观，却并不能揭示智能体内部能力的深浅差异，也难以指导在同一场景下如何逐步增强智能体的智能水平。

\begin{itemize}
    \item \textbf{对话式}：此类智能体通过自然语言与用户交互，精准理解意图、回答问题、提供信息，并执行简单指令，旨在构建流畅且高效的人机对话体验。典型应用包括客户服务（自动问答、售后支持）、信息检索（智能搜索）、个人日程管理与任务提醒，以及在线教育辅导。代表性产品如苹果的Siri、亚马逊的Alexa和谷歌助手，以及各类在线客服机器人。要使对话式智能体真正成功，关键在于强化自然语言理解（NLU）与自然语言生成（NLG）能力，并通过上下文管理模块保持多轮对话的连贯性，从而在适当时机提供符合语境的、有价值的响应。
    \item \textbf{任务型}：任务型智能体专注于在无需持续人工干预的前提下，自动化执行重复性或流程化操作，以追求卓越的效率与精度。其典型场景涵盖数据录入与迁移、审批流转、邮件自动分类与回复、报告自动生成以及日程安排等。常见示例包括自动化邮件回复系统和机器人流程自动化（RPA）工具。该类智能体的核心价值在于以人类难以匹敌的规模、速度或准确度完成预设任务，从而显著减轻人工负担，并最大限度降低人为错误风险。
    \item \textbf{具身智能}：具身智能智能体具有物理实体，通过摄像头、激光雷达等传感器实时感知环境，并依靠执行器（如电机、机械臂）在三维空间中自主行动。应用领域包括制造业（工业机器人装配与焊接）、物流与仓储（自动导引车 AGV、分拣机器人）、医疗保健（手术机器人、康复辅助机器人）以及交通运输（自动驾驶汽车、无人机配送）。代表案例有工厂协作机器人、特斯拉 Autopilot 系统和波士顿动力的仿生机器人。具身智能智能体的设计挑战在于将感知、认知、规划与控制等多种能力无缝融合，并在高度动态和不可预测的物理环境中实现安全、实时和鲁棒的运行。
    \item \textbf{决策型}：此类智能体借助大规模数据分析与模式挖掘，为用户提供个性化推荐、趋势预测或风险评估。典型应用涵盖电子商务（商品推荐）、内容平台（影视与新闻推荐）、金融服务（信用评分、欺诈检测、量化交易）以及医疗诊断辅助（医学影像风险预测）。代表性系统包括各大电商平台的推荐引擎和金融机构的信用风险模型。决策型智能体的核心优势在于其卓越的数据处理与模式识别能力，它们利用历史与实时数据挖掘潜在规律，其表现效果直接体现在推荐精准度、预测准确率和评估可靠性上，对用户体验与商业成果产生深远影响。
\end{itemize}

在介绍完主流分类之后，我们将视角聚焦到智能体的“认知与决策复杂度”这一纵深维度，从最简单的反应式规则，到引入世界模型的预测，再到能自我学习、具备社会或情感意识，直至构建分层混合与自治生态的复杂系统。这样一条“从简单反应到复杂认知”的演进路径，不仅帮助我们理解每种能力水平的设计难点和资源需求，也为后续在同一应用场景下，逐步提升智能体智能能力提供了清晰的技术路线图。在这里我们主要分为反应式智能体和认知型智能体。

反应式智能体是最简单的一类智能体，它们完全基于当前的感知输入直接做出反应，不依赖于复杂的内部状态表征或对历史信息的记忆。其行为模式通常由一组预定义的“条件\-动作”规则（Condition\-Action Rules）决定。

认知型智能体相比反应式智能体，具备了更复杂的内部结构和处理能力。它们能够维护关于世界状态的内部表征（Internal State），并基于这些内部状态以及更复杂的决策机制来进行行动。这使得它们能够更好地处理部分可观察的环境和需要依赖历史信息的任务。包括：基于模型的智能体、学习型智能体、社会型智能体。

混合型智能体在实际应用中，单一类型的智能体往往难以应对复杂多样的现实世界任务。因此，混合型智能体应运而生，它们通过结合不同类型智能体（如反应式、基于模型、学习型、社会型等）的特点和优势，在不同的认知层次或功能模块上采用不同的决策机制和处理策略，以期达到更优的整体性能。包括分层架构智能体和认知–情感智能体。

\subsection{对话式智能体}

这张图展示了一个多模态对话智能体的工作流程，具体如图\ref{fig:ch1_mlmchat}所示。

\input{chapters/ch1_multimodal_agent_workflow}

\begin{enumerate}[label=\arabic*.]
    \item 输入层：用户提供多模态对话上下文和用户指令，输入至多模态对话基础模型。
    \item 模型处理：该模型生成两部分输出：a. 文本形式的解决方案大纲。b.代码形式的动作序列。
    
    \item API 调用：a. API Selector从 API Platform（具备统一文档模式的大量 API 集合）中选取合适的 API。b. Action Executor基于 API执行动作序列，生成任务完成输出。

    \item 反馈优化：a. 用户对输出的反馈通过 RL from Human Feedback（RLHF）回传至基础模型，用于优化。b.任务完成输出也会反馈给 API Developers，便于改进API。

    \item 示例应用：图下方展示了该系统的应用场景，包括机器人、办公自动化、物联网等。

\end{enumerate}

整个流程体现了多模态对话智能体通过调用API执行任务，并借助人类反馈优化的闭环系统，适用于多种应用场景。

\subsubsection{任务型智能体}

图\ref{fig:ch1_data_proc}展示了一个完整的企业级数据处理架构流程，从数据源到最终用户的端到端数据处理管道，主要包含以下几个核心组件：

\input{chapters/ch1_data_processing_agent_workflow}

\begin{enumerate}[label=\arabic*.]
    \item 挑战性任务：任务要求是创建一个堆叠水平条形图，用于展示销售额前 10城市每个订单阶段的平均天数，并根据 “plot.yaml” 的设置将图表保存为 “result.png”。
    \item 复杂环境：展示了智能体的工作空间，其中包含了执行任务所需的各种元素，如markdown文件、yaml配置文件、生成的图表、数据库、表格文件、文档以及数据文件夹等。同时，环境还提供了智能体可以使用的工具，包括SQL查询语言、Python编程语言以及Bash命令行工具。
    \item 右侧的交互流程：a. 观察：智能体在执行动作后，会从环境中获得观察结果，包括标准输出、任务执行是否成功的标志，以及可能出现的错误信息；b. 动作：智能体根据其内部的“思考”过程（通常由LLM驱动）来决定采取何种动作。这些动作可以是执行Bash命令、运行Python脚本（通过指定文件路径和代码内容）、执行SQL操作（处理数据、生成输出），或者在任务完成时终止并返回结果。这个“思考\-动作\-观察”的过程形成了一个持续的反馈循环，直至任务成功完成。
\end{enumerate}

整体架构体现了现代企业级数据处理平台的完整生态，通过分层架构、标准化流程和自动化工具，实现了从原始数据到业务价值的高效转换，为数据驱动的业务决策提供了坚实的技术基础。

\subsubsection{具身智能体}

具身智能体（Embodied Agent）是人工智能系统中具备实体形态的智能体，能够在真实物理世界中感知环境、制定决策并自主行动。相比于仅在虚拟环境中工作的智能体，具身智能体不仅需要具备抽象的认知与决策能力，还需具备与真实环境互动的实时控制能力，通常包括传感器、执行器与决策控制单元。智能扫地机器人正是具身智能体的典型应用场景之一，它清晰体现了智能体在动态感知、实时决策与自主行动方面的核心优势。

智能扫地机器人不仅需要具备路径规划与避障能力，还需要实时感知房间的环境变化，包括障碍物的出现、地板的材质变化和家具的位置调整。扫地机器人通常搭载激光雷达（LiDAR）、红外传感器、摄像头等多种传感器实时感知环境状态，将感知信息送入决策模块，进而决定行动路线、清扫模式以及应对紧急情况的策略。

扫地机器人选择智能体系统而非简单工作流或单次模型调用的核心原因在于其所处环境的高度动态性与不确定性。家庭环境往往杂乱且经常变化，传统固定流程（例如沿预定轨迹移动的简单规则）显然不足以高效且安全地应对突发情况与环境变化。同时，单纯的大语言模型调用也无法实时处理传感器产生的海量数据，更不能实时执行对物理世界的行动。相反，智能体的动态决策、自主适应与实时反馈调整能力，使其在复杂物理环境下表现出显著优势。

以实际情境为例，当扫地机器人进入客厅区域后，传感器实时检测到前方突然出现儿童玩具或宠物时，机器人首先通过感知系统捕捉当前场景信息，随后将信息输入智能决策模块，这一模块借助强化学习（RL）算法或者基于模型的动态路径规划方法，实时更新下一步的清扫策略。例如，可能会选择绕行障碍物，或者暂停并发出警报以提醒用户清理障碍物。在实时行动过程中，扫地机器人持续评估自身决策效果，动态优化路径规划模型与避障策略，形成从“感知—决策—行动—反馈”完整的闭环，这一闭环正是智能体系统的典型特征。

为了清晰展现智能扫地机器人的决策过程与系统架构，以下采用流程图进行示意：

\begin{figure}[h!t]\centering
  \includegraphics[width=.9\linewidth]{imgs/ch1/clean_robot.png}
  \caption{扫地机器人决策过程与系统架构。}
  \label{fig:ch1_cr}
\end{figure}

从流程图可以看出，智能决策模块在整个决策闭环中起到了关键作用，决定着清扫机器人的行动路线、策略选择以及动态调整；而传感器信息获取、基础动作执行（如移动与清扫）等固定且明确的动作则可以通过简单的规则控制或机械执行流程实现。这种清晰的智能决策模块与流程模块的区分，也为具身智能体在工程实现中提供了重要的设计指导。

进一步，从软件实现的视角，可以将智能扫地机器人的核心决策逻辑抽象为以下伪代码描述：

\begin{algorithm}[H]
\caption{智能扫地机器人控制算法}
\begin{algorithmic}[1]
\STATE \textbf{初始化扫地机器人}
\STATE 环境状态 $\leftarrow$ 获取初始环境信息()
\WHILE{未完成清扫任务}
    \STATE 当前状态 $\leftarrow$ 获取传感器实时数据()
    \IF{当前状态含障碍物}
        \STATE 决策 $\leftarrow$ 智能决策模块(当前状态, 历史反馈信息)
        \STATE 动作 $\leftarrow$ 根据决策执行避障策略()
    \ELSE
        \STATE 动作 $\leftarrow$ 按原规划路径继续执行()
    \ENDIF
    \STATE 执行动作()
    \STATE 实时评估执行效果()
    \STATE 更新历史反馈信息()
\ENDWHILE
\STATE 返回充电站充电()
\end{algorithmic}
\end{algorithm}

从上述伪代码可清晰看出，智能决策模块涉及到动态状态感知、历史反馈信息分析和实时策略调整，这显然超出了简单静态流程或单纯模型调用所能处理的范围，必须通过智能体系统实现。同时，感知系统与执行系统则属于标准化流程，适合由简单明确的工程化模块完成。

综上所述，智能扫地机器人作为典型的具身智能体实例，完美体现了智能体系统在实时动态环境感知、多步决策和持续反馈优化方面的核心价值，成为了展示具身智能体实际应用的重要范例。

\subsubsection{决策型智能体}

在商业智能领域，智能体可以作为高效的信息检索和分析助手辅助决策，帮助用户从海量数据中快速获取洞察帮助用户从海量数据中快速获取洞察。图\ref{fig:ch1_ba}展示了一个典型的商业助手场景下，基于智能体的智能信息检索流程及其应用案例。

\begin{figure}[h!t]\centering
    \includegraphics[width=.9\linewidth]{imgs/ch1/wps_doc_2.png}
    \caption{商业助手智能体的智能信息检索流程}
    \label{fig:ch1_ba}
\end{figure}

如图左侧展示的是信息处理逻辑框架，包括以下部分：

% TT: 图文不符，而且正文应该用括号英文解释前面中文，有必要的时候才这样
\begin{enumerate}[label=\arabic*.]
    \item 问题提出（Question）：用户向智能体提出具体的商业查询需求，例如，“查询公司2024年第一季度的销售额和相应的营销费用是多少？”

    \item 信息处理核心（Agent）：智能体接收到问题后，启动信息处理流程。它能够接入并处理多种格式的文档数据源，如PDF格式的财务报告、JPG格式的图表图片等。智能体利用其内部的文档理解和信息抽取能力，将这些异构数据转换为结构化的、易于分析的格式，如CSV或TXT文件。

    \item 信息整合（Information Integration）：智能体对从不同来源抽取和处理后的信息进行整合与关联分析。例如，将不同月份的销售数据和营销费用数据进行汇总。
    
    \item 答案生成（Answer）：基于整合分析后的信息，智能体生成对用户问题的直接解答。
\end{enumerate}

如图右侧展示的是具体应用案例——Q1财务数据分析，针对用户问题，智能体输出文字说明：“在2024年第一季度，公司总销售额达到\$600,000，相应的营销费用总计\$105,000。这一数据显示了公司在本季度为支持市场扩张策略而进行的显著营销投入。业绩指标同时反映了销售额的增长以及营销费用的相应增加。”同时，智能体会清晰标注答案所依据的参考来源，例如”公司财务报告”和”公司财务数据”。

针对数据分析与验证，业绩指标说明：智能体可能会进一步解释这些数据的意义，例如，“业绩指标显示了销售额的增长以及为支持此扩张而增加的营销费用。”表格数据展示：清晰列出明细数据，如：

\begin{table}
    \centering
    \begin{tabular}{|c|c|c|}
    \hline
    月份 & 销售额（Sales） & 营销费用（Marketing） \\
    \hline
    一月 & \$150,000 & \$40,000 \\ \hline
    二月 & \$200,000 & \$45,000 \\ \hline
    三月 & \$250,000 & \$20,000 \\
    \hline
    \end{tabular}
\end{table}

计算逻辑验证方面，智能体内部可能包含或生成简单的计算脚本（如Python代码片段）来对数据进行求和或验证，确保最终结果的准确性。例如，定义一个calculate()函数对月度销售额和营销费用分别求和。针对原始问题复述，智能体的输出通常也会包含用户提出的原始问题，以便用户核对。

此案例通过图文结合的方式，生动展示了智能体如何从用户提出的自然语言问题出发，通过自主的数据查找、处理、整合与分析，最终提供结构清晰、数据准确的智能解答，体现了其在商业智能助手场景中的巨大潜力。

\subsubsection{混合型智能体}

\begin{figure}[h!t]\centering
  \includegraphics[width=.9\linewidth]{imgs/ch1/wps_doc_9.png}
  \caption{AI新闻系统框架}
  \label{fig:ch1_news}
\end{figure}

图\ref{fig:ch1_news}以“AI新闻系统框架”为例，展示多智能体如何在新闻自动化生产中协同工作。AI新闻系统框架分为三个连续阶段，以不同角色的智能体协同完成从新闻起草到社会反馈分析的全流程自动化支持。首先，在新闻稿起草阶段，用户提交主题需求，例如“请根据给定主题撰写一篇新闻稿”。随即，搜索者A智能体从新闻数据库、事实数据库和万维网中提取相关信息，初步汇总并标注来源。写作者智能体接收这些汇总结果后，构思标题并撰写新闻稿初稿。

接下来是新闻稿润色阶段，审阅者智能体根据稿件类型（如采访类新闻）对结构、语言和事实准确性提出评论和修改建议。在此基础上，重写者智能体生成修订版本，并与审阅者的反馈进行对比；这一润色-重写循环可多次迭代，直至稿件质量达到预期要求。

最后，系统进入模拟与分析阶段，通过生成虚拟人物档案，模拟不同背景和观点的读者；随后，AI为这些虚拟人物生成评论，例如Fiona或Abby的反馈；最终，系统对所有评论进行词频统计、情感分析和立场分析，以评估新闻稿可能引发的社会反响和潜在影响。

\section{什么时候需要构建智能体？}

智能体以其显著的自主性、自适应性和工具协作能力，在多个领域展现出强大的应用潜力。然而，并非所有任务都适合构建和部署智能体系统。理解智能体的核心价值、认识常见误区，并明确何时使用智能体变得尤为关键。智能体的核心价值在于其卓越的动态决策能力、上下文敏感响应和跨系统工具协调。智能体能够在环境动态变化时，自主感知和调整决策，避免了传统静态工作流或简单模型无法及时适应复杂环境的局限性。此外，智能体还具备自我学习和持续优化的能力，可以根据用户反馈或环境变化动态优化策略和执行流程。

然而，智能体并不是万能的，常见的误区包括认为智能体能解决所有自动化问题，或者忽视智能体系统在实时性、成本和稳定性方面可能带来的挑战。智能体系统的构建往往需要显著的技术投入、计算资源和维护成本，如果任务的需求简单、明确且稳定，使用传统的自动化工具或简单模型调用可能更为经济高效。明确何时选择构建智能体，不仅对提高企业或项目的效率至关重要，更对资源分配、风险控制和业务创新具有深远影响。因此，在实际应用中，应当谨慎评估任务特点和环境约束，理性决策是否使用智能体，以实现资源的最佳利用和最大化的商业价值。接下来，我们将深入探讨智能体的适用场景和不适用场景，并提供详细的决策框架与实施建议，以帮助读者更好地理解和掌握智能体构建的实际应用策略。

\subsection{智能体适用场景}

在实际应用中，智能体最适合用于环境具有高度动态性、任务决策逻辑复杂且多变、需要跨系统或工具协调互动，以及要求系统能够通过持续反馈不断优化自身表现的场景。这些场景通常涉及复杂的信息分析、多步决策过程和实时响应需求，传统静态流程或单纯调用模型方法难以有效解决，因此智能体系统在此类应用中展现出独特的优势和价值。

\textbf{动态、多步骤任务的优势。}智能体特别适合处理复杂且包含多个步骤的任务，这类任务通常具有动态变化、多源数据整合以及复杂决策路径等特点。在金融领域，智能体被广泛应用于投资组合管理与分析。具体来说，智能体可以实时抓取和分析来自股市、债市、外汇市场的海量数据，迅速识别市场趋势和潜在的风险因素，并动态调整资产配置方案。例如，一家资产管理公司使用智能体系统实时追踪全球市场动态，运用高级风险评估和预测模型，定期调整投资组合中股票、债券与现金比例。这种实时的动态优化机制不仅帮助投资团队及时把握投资机会，也显著降低了市场波动带来的风险。

另一个例子是复杂的技术支持系统，当用户遇到问题时，智能体可以实时分析用户描述的问题类型和技术参数，动态调用相应的技术文档、故障排除工具和专家知识库进行精准的响应，从而提高问题解决效率和用户满意度。

再如，在复杂的科研项目中，智能体可实时整合和分析多个数据源（如实验数据、文献资料和科研数据库），动态规划研究流程、自动生成实验方案，并及时根据实验结果调整后续研究路径，大幅提升科研效率和成果产出质量。

\textbf{上下文敏感决策的智能响应。}上下文敏感决策的核心在于实时捕获和分析环境反馈，动态调整决策路径，智能推荐系统是典型应用案例之一。以在线视频平台为例，其智能推荐系统能够实时追踪用户的观看行为，如观看视频的类型、播放时间、互动反馈、甚至暂停和回看的行为模式，通过智能体系统实时调用用户历史数据、实时行为和外部内容数据，动态地更新个性化推荐策略，从而提升用户体验和黏性。

此外，电商平台的实时推荐智能体能够根据用户的浏览历史和实时点击行为，结合实时库存、促销活动等外部数据，动态调用不同推荐引擎，自动调整商品推荐策略和展示位置，使得用户更容易发现并购买感兴趣的产品。例如，在在线广告平台中，智能体能够实时分析用户行为和广告效果反馈，动态调整广告投放策略，最大化广告收益和用户体验。

\textbf{多工具协作与复杂流程的高效管理。}在涉及多系统、多工具协作的复杂业务流程中，智能体展现了强大的协调和管理能力。一个典型的应用场景是综合客户服务系统，尤其在电子商务领域表现突出。例如，某大型电商企业使用智能体管理客户服务流程，智能体可以高效地调用CRM系统识别客户的历史数据，检查订单管理系统的实时订单状态，同时调用知识库系统快速回答客户提出的复杂问题。此外，智能体还能根据客户互动中的新问题自动更新知识库，形成闭环反馈机制，持续提高服务的精准性和效率，极大地降低了客服人员的工作负担，提高了客户满意度。

\textbf{交互式学习与自适应优化。}当任务需要智能系统持续交互反馈并自适应优化时，智能体能发挥其优势，持续提高系统性能。在教育科技领域，智能体表现出极大的应用价值。例如，一家在线教育公司开发的个性化学习助手，能够实时监控学生在学习数学、科学等学科的表现，捕捉答题时间、错误类型、学习进度等多种指标。基于实时分析结果，智能体会自动调整下一阶段的学习计划，包括内容难度、讲解方式和练习题型，最大化地提高学习效率和成果。

此外，在智能客服培训系统中，智能体能够通过模拟客户互动场景，根据客服人员实时的应对表现动态生成个性化培训建议，有效提高客服人员的服务技能和客户满意度。

\subsection{智能体不适用场景}

尽管智能体具有明显的优势，但在某些情境下使用智能体未必最佳。具体而言，任务简单明确、实时性能要求极高、预算限制严格或稳定性要求极高时，智能体并非理想选择。

\textbf{简单、固定的流程任务。}固定的任务，如自动邮件分类或标准数据转换，可以简单地通过单一的模型调用或传统的规则引擎高效完成。例如，小型在线商店收到的客户邮件通常内容固定且明确，单个分类步骤足以满足要求，无需复杂的智能体。公司内部知识库查询同样如此，直接调用LLM与简单文档检索工具结合，就足以满足员工的快速信息查询需求。

\textbf{实时性和低延迟要求。}当任务对实时响应要求非常高时，智能体可能由于固有的延迟和计算负载难以满足需求。例如实时聊天机器人或交互式UI界面，此时更适合使用轻量级的模型调用或优化后的单一LLM服务，确保极低的响应延迟。

\textbf{预算敏感和高吞吐量任务。}预算严格或需要处理大量任务的场景中，智能体系统通常意味着更高的资源开销。例如企业内部批量数据处理或高频API调用任务，通过传统自动化或单纯模型调用可实现更好的经济效益，智能体的额外开销并不值得。

\textbf{高精度与稳定性要求。}在医疗诊断、航空控制系统等对精确性和稳定性要求极高的领域，当前智能体受限于模型能力的幻觉问题，无法确保绝对准确和确定的结果。因此，传统的规则系统和明确的算法程序可以提供更可靠和确定的输出，避免因智能体决策偏差导致的严重后果。

\textbf{智能体决策逻辑流程图示例。}以下流程图\ref{fig:agent_kefu}清晰展示了智能客户服务助手的智能体与传统工具模块协作流程：

\begin{figure}[H]
  \centering
  \includegraphics[width=0.6\textwidth]{imgs/ch1/agent_kefu.png}
  \caption{智能客户服务助手的智能体与传统工具模块协作流程}
  \label{fig:agent_kefu}
\end{figure}

通过以上图示，可以清晰看出，智能体承担了问题识别、响应规划、回复整合和质量评估的动态决策过程，而订单查询、退款处理和知识检索等工具调用属于简单且固定的流程步骤，更适合采用静态的API调用。

在确定是否使用智能体时，可以参考以下决策问题：

\begin{itemize}
\item 单一Prompt能否满足需求？
\item 静态流程能否高效完成任务？
\item 智能体带来的灵活性是否值得额外的成本与延迟？
\item 任务是否真正需要动态决策？
\end{itemize}

总之，智能体系统的选择必须基于明确的业务需求和技术限制，通常应以最简单的可行解决方案为起点，只有在确认智能体带来的额外复杂性具有明确价值时，才应实施智能体方案。随着大语言模型及相关技术的持续发展，当前限制可能逐步减轻，但现阶段仍需谨慎决策，以实现最佳的资源配置和效益最大化。

% \section{评估、安全、自适应和持续运行}
% TODO: 评估和安全性应该先讲，然后是自适应和持续运