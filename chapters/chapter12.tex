\chapter{自动化求职智能体}
\label{cha:automating_job_search_agent}

\section{背景介绍}
\label{sec:background_job_search}
求职是每个人职业生涯中都会面临的重要挑战。传统的求职方式往往效率低下：手动筛选海量招聘信息、反复修改简历和求职信、追踪申请进度等烦琐任务让求职者应接不暇，同时还容易遗漏优质机会。如果我们能够构建一个智能体来自动化这个过程会怎样？本章将从需求分析到系统实现，从功能验证到效果评估，带领读者探索智能体应用开发的完整方法论。

本章将介绍一种强大的解决方案：利用人工智能（人工智能）和浏览器自动化技术，大幅简化你的求职流程。我们将深入探讨一个基于 \texttt{browser-use} 库构建的 Python 脚本，打造一个可自动搜索、分析、记录职位并辅助上传简历的智能智能体。这个工具能让你创建智能智能体，这些智能体能够根据你的指令浏览网站、提取关键信息，甚至与网页元素进行交互，而这一切都由大型语言模型（大语言模型）驱动。

项目目标：
\begin{itemize}
    \item 自动化搜索和筛选数据分析相关职位
    \item 智能提取职位信息和技术要求
    \item 自动上传简历和填写申请表单
    \item 生成技术栈思维导图和求职分析报告
    \item 提高求职效率，减少重复性工作
\end{itemize}

本章将带你逐行解读提供的演示脚本，解释其各个组成部分以及它们如何协同工作以实现上述目标。

\section{技术框架}
\label{sec:tech_stack_job_search}

\texttt{browser-use}是本项目的核心组件，它是一个专为构建智能体浏览器交互而设计的Python库。根据其GitHub页面介绍，该库通过集成大语言模型来实现智能化的网页操作。其核心特性包括：

\begin{itemize}
    \item \textbf{智能网页理解}：基于视觉和文本双重分析，准确理解网页结构和内容语义
    \item \textbf{自然语言交互}：支持使用自然语言指令直接控制浏览器操作，降低使用门槛
    \item \textbf{多模态感知}：融合视觉信息和文本内容，实现更准确的页面元素识别和操作决策
    \item \textbf{动态适应性}：具备处理不同网站布局和交互模式的能力，适应性强
    \item \textbf{智能容错}：内置异常处理和重试机制，提高任务执行的稳定性和成功率
\end{itemize}

除了核心的browser-use库外，还集成了以下主要组件：
\begin{itemize}
    \item \textbf{LangChain}：大语言模型集成框架，负责对话管理和任务编排
    \item \textbf{Google Gemini API}：提供强大的自然语言理解和生成能力
    \item \textbf{PyPDF2}：PDF文档处理库，用于解析简历文件内容
    \item \textbf{Pydantic}：数据验证和序列化框架，确保数据类型安全
    \item \textbf{Playwright}：底层浏览器控制引擎，提供跨平台浏览器操作
\end{itemize}



\section{项目准备}
\label{sec:project_setup_job_search}

\subsection{前置准备与环境配置}
\label{ssec:prerequisites_env_setup}
要成功运行本章的演示脚本并进行后续开发，你需要准备以下环境和工具：

\subsubsection{browser-use快速开始}

\textbf{版本要求：}本章基于 \texttt{browser-use} 版本 \texttt{0.1.40} 进行演示。

\textbf{使用pip安装（Python>=3.11）：}

\begin{verbatim}
pip install browser-use
\end{verbatim}

\textbf{安装浏览器：}

\begin{verbatim}
playwright install chromium --with-deps --no-shell
\end{verbatim}

\textbf{启动智能体：}

\begin{verbatim}
import asyncio
from dotenv import load_dotenv
load_dotenv()
from browser_use import Agent
from browser_use.llm import ChatOpenAI

async def main():
    agent = Agent(
        task="Compare the price of gpt-4o and DeepSeek-V3",
        llm=ChatOpenAI(model="gpt-4o"),
    )
    await agent.run()

asyncio.run(main())
\end{verbatim}

\textbf{API密钥配置：}将您要使用的提供商的API密钥添加到 \texttt{.env} 文件中：

\begin{verbatim}
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_KEY=
GOOGLE_API_KEY=
DEEPSEEK_API_KEY=
GROK_API_KEY=
NOVITA_API_KEY=
\end{verbatim}

\subsubsection{环境配置详细说明}

在使用browser-use之前，需要配置相应的大语言模型API密钥。本章脚本展示了Google Gemini和OpenAI的配置示例，你需要获取计划使用的大语言模型的应用程序接口密钥。为了安全起见，强烈建议通过环境变量的方式设置这些密钥，而不是直接写入代码中。

\begin{itemize}
    \item \textbf{项目文件结构：}确保项目目录包含必要的文件，如简历PDF文件、配置文件等

    \item \textbf{开发者注意（项目构建与测试命令参考）：} 如果您想对 \texttt{browser-use} 项目本身进行开发或贡献，以下命令可能会用到（通常使用 \texttt{uv} 或 \texttt{pip} 管理依赖和运行任务）：
    \begin{verbatim}
# 尝试运行 browser-use 库中的示例 (具体命令取决于项目结构）
# uv run examples/simple.py

# 格式化代码 (以 ruff 为例）
# uv run ruff format path/to/your_script.py

# 运行测试 (以 pytest 为例）
# uv run pytest

# 构建项目 (如果适用）
# uv build
    \end{verbatim}
    对于本章的脚本用户，主要关注点是正确安装依赖并运行脚本。
\end{itemize}

\subsection{代码详解：求职自动化脚本}
\label{ssec:code_walkthrough_job_search}
现在，让我们逐段分析这个 Python 脚本。
\begin{verbatim}
"""
coding=utf-8
Try demos in the example library with uv run examples/simple.py
Run the linter/formatter with uv run ruff format examples/some/file.py
Run tests with uv run pytest
Build the package with uv build
"""
import asyncio
import csv
import logging
import os
import base64
import sys
import json
from datetime import datetime
from pathlib import Path
from langchain_openai import AzureChatOpenAI, ChatOpenAI
# from langchain_ollama import ChatOllama # 在最终的LLM设置中未使用
# from langchain_deepseek import ChatDeepSeek # 未使用
from langchain_anthropic import ChatAnthropic # 已导入但在LLM设置中未使用
from patchright.async_api import async_playwright # 底层的playwright访问
from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use.agent.memory.views import MemoryConfig # 用于记忆功能
from browser_use.agent.views import (
    REQUIRED_LLM_API_ENV_VARS,
    ActionResult,
    AgentError,
    AgentHistory,
    AgentHistoryList,
    AgentOutput,
    AgentSettings,
    AgentState,
    AgentStepInfo,
    StepMetadata,
    ToolCallingMethod,
)
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pydantic import BaseModel, SecretStr
from PyPDF2 import PdfReader
from browser_use import ActionResult, Agent, Controller # browser-use核心组件
from browser_use.browser.browser import Browser, BrowserConfig
from browser_use.browser.context import BrowserContext
# from lmnr import Laminar # 已导入但被注释掉
# Laminar.initialize(project_api_key=project_api_key)
# from mem0 import MemoryClient # 已导入但被注释掉
# client = MemoryClient(api_key="[MEM0_API_KEY]")
\end{verbatim}

\subsubsection{导入模块与初始设置}
\label{sssec:imports_initial_setup}
这部分代码导入了所有必需的库，包括标准库、Langchain 相关库、\texttt{browser-use} 核心组件以及 \texttt{pydantic} 和 \texttt{PyPDF2}。

\begin{verbatim}
# 设置日志记录
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("job_search.log"), logging.StreamHandler()],
)

# 一个服务的API密钥 (根据注释掉的代码，可能是Laminar)
_project_api_key_b64 = (
    "[PROJECT_API_KEY_BASE64_PART1]"
    "[PROJECT_API_KEY_BASE64_PART2]"
)
project_api_key = base64.b64decode(_project_api_key_b64).decode("utf-8")
print(project_api_key) # 打印解码后的密钥 - 处理敏感数据时需谨慎

# 申请记录文件 (此处的APPLICATIONS_FILE对应 job_applications.csv)
APPLICATIONS_FILE = "job_applications.csv" # 用于存储职位申请信息
APPLICATION_STATS_FILE = "application_stats.json" # 用于存储统计信息

# 确保CSV文件存在
if not os.path.exists(APPLICATIONS_FILE):
    with open(APPLICATIONS_FILE, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        # 定义CSV文件的表头：职位, 公司, 链接, 薪资, 地点, 申请时间
        writer.writerow(["职位", "公司", "链接", "薪资", "地点", "申请时间"])

logger = logging.getLogger(__name__)
controller = Controller() # Controller 实例化

# 注意：这是你的简历文件路径
CV = Path.cwd() / "resume_template.pdf" # 模型与控制器配置的一部分
if not CV.exists():
    raise FileNotFoundError(
        f"你需要设置简历文件的路径到CV变量。在 {CV} 未找到简历文件。"
    )
\end{verbatim}
日志配置、应用程序接口 密钥处理、文件路径定义：与前述分析一致。

\texttt{Controller} 与 CV 路径：这是模型与控制器配置的关键部分，\texttt{Controller} 用于注册智能体可调用的动作，\texttt{CV} 指定了简历文件的位置。

\subsubsection{动作设计：用户智能体的能力模块（定义自定义智能体 Action)}
\label{sssec:action_design_agent_capabilities}
\texttt{@controller.action(...)} 装饰器用于将 Python 函数暴露为 \texttt{browser-use} 智能体可以使用的工具。这些动作构成了智能体的核心能力。

\begin{verbatim}
class Job(BaseModel):
    title: str # 职位名称
    link: str # 职位链接
    company: str # 公司名称
    fit_score: float # 与个人资料的匹配度评分 (脚本中定义但未完全实现评分逻辑)
    location: str | None = None # 工作地点
    salary: str | None = None # 薪资范围

# 动作1: 保存与读取职位信息
@controller.action(
    "Save jobs to file - with a score how well it fits to my profile",  # 动作描述，供LLM理解
    param_model=Job # 指定参数模型
)
def save_jobs(job: Job):
    # 将收集到的职位信息保存到 jobs.csv
    with open("jobs.csv", "a", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow([
            job.title, job.company, job.link, job.salary, job.location
        ])
    return "Saved job to file" # 返回给agent的执行结果信息

@controller.action("Read jobs from file") # 从文件读取已保存职位
def read_jobs():
    with open("jobs.csv") as f: # 从 "jobs.csv" 读取
        return f.read()

# 动作2: 读取简历内容
@controller.action("Read my cv for context to fill forms") # 读取简历内容以供填写表单
def read_cv():
    # 将 PDF 简历读取成文字内容，供模型记忆与填写表单用
    pdf = PdfReader(CV)
    text = ""
    for page in pdf.pages:
        text += page.extract_text() or ""
    logger.info(f"已读取简历，包含 {len(text)} 个字符")
    # ActionResult 的 include_in_memory=True 使简历内容可供agent后续步骤使用
    return ActionResult(extracted_content=text, include_in_memory=True)

# 动作3: 上传简历
@controller.action(
    # 动作描述更新以匹配新内容
    ("Upload cv to element - call this function to upload if element is not "
     "found, try with different index of the same upload element"),
)
async def upload_cv(index: int, browser: BrowserContext): # 异步函数，模拟用户上传简历
    path = str(CV.absolute())
    dom_el = await browser.get_dom_element_by_index(index) # 通过索引获取DOM元素
    if dom_el is None:
        return ActionResult(error=f"在索引 {index} 未找到元素")
    # 获取文件，上传类型的DOM元素
    file_upload_dom_el = dom_el.get_file_upload_element() 
    if file_upload_dom_el is None:
        logger.info(f"在索引 {index} 未找到文件上传元素")
        return ActionResult(error=f"在索引 {index} 未找到文件上传元素")
    file_upload_el = await browser.get_locate_element(
        file_upload_dom_el
    )  # 定位元素
    if file_upload_el is None:
        logger.info(f"在索引 {index} 未找到可定位的文件上传元素")
        return ActionResult(
            error=f"在索引 {index} 未找到可定位的文件上传元素"
        )
    try:
        await file_upload_el.set_input_files(path) # 设置输入文件 (上传)
        msg = f'成功上传文件 "{path}" 到索引 {index}'
        logger.info(msg)
        return ActionResult(extracted_content=msg)
    except Exception as e:
        logger.debug(f"set_input_files 时发生错误: {str(e)}")
        return ActionResult(error=f"上传文件到索引 {index} 失败")

# 动作4: 上传个人头像 (支持上传头像)
@controller.action(
    "Upload profile picture to element", # 上传个人头像到指定元素
)
async def upload_profile_picture(index: int, browser: BrowserContext):
    path = os.path.join(os.getcwd(), "profile_picture.png") # 假设图片名称固定
    if not os.path.exists(path):
        return ActionResult(error=f"图片文件未找到: {path}", is_done=True)
    valid_extensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"]
    _, ext = os.path.splitext(path.lower())
    if ext not in valid_extensions:
        return ActionResult(
            error=f"无效的图片文件扩展名: {ext}. 必须是 {valid_extensions}之一",
            is_done=True,
        )
    logger.warning(
        "动作 'upload_profile_picture' 调用了 'upload_file'，"
        "但此脚本中未定义该动作。若无通用的 'upload_file' 动作，"
        "此部分可能无法按预期工作。"
    )
    return await controller.execute_action(
        "upload_file", {"index": index, "file_path": path}
    )
\end{verbatim}
此部分详细介绍了智能体的核心能力模块，包括保存和读取职位信息、读取简历内容以及上传简历和头像。

\subsubsection{主任务入口与执行逻辑}
\label{sssec:main_task_entry_execution}
这是配置和运行智能体的核心部分，定义了任务目标与行动计划。
\begin{verbatim}
async def main():
    # 检查已分析的职位数量
    total_applications = 0
    if os.path.exists(APPLICATION_STATS_FILE):
        try:
            with open(APPLICATION_STATS_FILE, "r", encoding="utf-8") as f:
                stats = json.load(f)
                total_applications = stats.get("total_applications", 0)
        except Exception as e:
            logger.error(f"读取“申请/分析”统计文件时出错: {e}")

    logger.info(f"当前已分析数据分析职位数量: {total_applications}/10000")

    if total_applications >= 10000:
        logger.info("✅ 已达到目标分析数量(10000)，任务完成！")
        return

    # 基础任务描述 (ground_task) - 定义任务目标与行动计划
    ground_task = (
        "你是一个专业的AI求职助手，专门为刚毕业的数据分析硕士研究生提供求职支持。"
        "我是一名数据分析硕士毕业生，目标是在香港地区寻找金融行业的相关职位。"
        "请帮我搜索并分析50个相关职位，并创建技术栈思维导图。"
        "\n\n以下是详细的指导原则：\n"
        "1. 招聘网站搜索策略："
        "   - 在LinkedIn、Indeed、Glassdoor等主流招聘网站上搜索职位"
        "   - 操作步骤：首先刷新网页，在搜索框中输入关键词，然后点击搜索按钮"
        "   - 推荐搜索关键词：'合规分析师'、'KYC专员'、'客服等专员'、'数据分析师'"
        "\n"
        "2. 职位信息提取要求："
        "   对于每个职位描述，请提取以下完整信息："
        "   - 职位名称"
        "   - 公司名称"
        "   - 工作地点（远程/现场/混合模式）"
        "   - 所需技能和技术要求"
        "   - 经验要求"
        "   - 职责描述"
        "   - 薪资范围（如有明确标注）"
        "   - 职位链接"
        "\n"
        "3. 目标地区和行业筛选："
        "   - 优先地区：香港（主要目标）"
        "   - 次选地区：大湾区其他城市"
        "   - 目标行业：证券、银行、支付等金融服务业"
        "   - 目标岗位：合规、KYC、客服等相关职位"
        "\n"
        "4. 职位筛选标准："
        "   - 薪资范围：15-20k港币左右（不追求过高薪资）"
        "   - 公司规模：优先考虑中小型公司（竞争相对较小）"
        "   - 经验要求：3年左右工作经验的职位"
        "\n"
        "5. 技术栈分析重点："
        "   特别关注并提取以下技术要求："
        "   - 编程语言：Python、JavaScript、SQL等"
        "   - AI/ML框架：TensorFlow、PyTorch、LangChain等"
        "   - API集成：OpenAI API、Claude API等"
        "   - 云服务平台：AWS、Azure、Google Cloud Platform"
        "   - DevOps/MLOps工具"
        "   - 数据库技术：MySQL、PostgreSQL、MongoDB等"
        "   - Web开发框架：React、Vue、Django等"
        "\n"
        "6. 数据整理和分析："
        "   - 将收集到的信息整理成结构化数据格式"
        "   - 准备创建技术栈思维导图的基础数据"
        "   - 分析不同职位之间的技术要求差异"
        "   - 识别核心技能与可选技能"
        "   - 记录所有找到的职位信息，避免重复分析"
        "\n"
        "7. 执行策略："
        "   - 如果遇到任何技术障碍，请灵活调整搜索策略"
        "   - 保持搜索的系统性和全面性"
        f"   - 目标：分析至少100个相关职位描述"
        f"   - 当前进度：已分析{total_applications}个职位"
        f"   - 剩余目标：还需分析{10000-total_applications}个职位"
    )

    # 账户信息 (account_profile)
    account_profile = (
        "\n8. 账户登录和个人信息："
        "   - 如果网站需要登录，优先使用Google账户登录"
        "   - LinkedIn个人资料链接：[已隐藏]"
        "   - 联系邮箱：[邮箱地址]@example.com"
        "   - 用户姓名：[用户姓名]"
        "   - 联系电话：[电话号码]"
        "   - 所在地区：[地理位置]"
        "   - 简历文件：使用resume_template.pdf作为简历附件上传"
        "\n"
    )
    
    # 记忆指令 (memory) - "提取职位名称、公司名称、技术要求等内容"
    memory_instructions = ( # Renamed to avoid conflict with 'memory' module
        "\n9. 信息提取和记忆要求："
        "   对于每个职位描述，请系统性地提取并保存以下信息："
        "   1. 基本信息：职位名称、公司名称、工作地点"
        "   2. 技术要求：所需技能和技术栈（详细列出每一项）"
        "   3. 职业要求：经验要求和具体职责描述"
        "   4. 薪酬信息：薪资范围（如有明确标注）"
        "   5. 申请渠道：职位链接和申请方式"
        "   6. 数据格式：将这些信息整理成结构化格式"
        "   7. 中文总结：用中文总结每个职位的主要技术要求和核心职责"
        "\n"
    )
    
    # 行动指令 (action) - "在LinkedIn、Indeed 等平台搜索关键词，采集数据"
    action_instructions = ( # Renamed to avoid conflict
        "\n10. 具体执行策略："
        "    搜索执行步骤："
        "    1. 多平台搜索：在不同招聘网站上搜索相关职位"
        "       推荐搜索关键词包括："
        "       - '数据分析师'"
        "       - '合规分析师'"
        "       - 'KYC专员'"
        "       - '客服等专员'"
        "       - 'AI助手开发工程师'"
        "       - '风险控制专员'"
        "    2. 职位浏览：对于每个职位，点击查看详细描述"
        "       注意：仅浏览和收集信息，暂不申请职位"
        "    3. 信息采集：系统性地提取职位相关信息"
        "\n"
        "    数据整理要求："
        "    1. 技术栈分类：将收集到的技术栈信息按类别分组"
        "       - 编程语言类（Python、JavaScript、SQL等）"
        "       - 框架和库类（React、Django、TensorFlow等）"
        "       - API和服务类（OpenAI API、云服务等）"
        "       - 工具和平台类（Git、Docker、Kubernetes等）"
        "    2. 数据验证：确保收集信息的准确性和完整性"
        "    3. 去重处理：避免重复收集相同职位信息"
        "\n"
        "    异常处理策略："
        "    - 如果遇到页面变更和元素识别问题，放弃当前页面"
        "    - 返回搜索结果页面，继续处理下一个职位"
        "    - 保持程序持续运行，如果任务未完成则重新执行"
        "    - 必要时重新打开网页，使用新的搜索关键词"
        "\n"
    )

    # 反思指令 (reflection)
    reflection_instructions = ( # Renamed
        "\n11. 错误处理和反思机制："
        "    失败恢复策略："
        "    - 如果任务执行失败，立即重新开始整个流程"
        "    - 重新打开网页，重新输入搜索关键词"
        "    - 不从之前的断点继续执行，确保流程的完整性"
        "\n"
        "    质量控制要求："
        "    - 定期检查收集数据的质量和完整性"
        "    - 验证职位信息的准确性和时效性"
        "    - 确保技术栈提取的全面性和准确性"
        "\n"
        "    持续改进机制："
        "    - 根据搜索结果调整关键词策略"
        "    - 优化信息提取的准确性和效率"
        "    - 不断完善异常处理和错误恢复机制"
        "\n"
    )

    # 组合所有指令
    # 注意：原始脚本中 memory, action, reflection 是变量名，
    # 这里改为更具描述性的名称
    ground_task = (
        ground_task + memory_instructions + account_profile
        + action_instructions + reflection_instructions
    )

    # LLM 配置: 支持多种大模型 API，
    # 可使用如 ChatOpenAI, ChatAnthropic, ChatGoogleGenerativeAI
    # (脚本中已包含 Gemini 和 OpenAI via GitHub 的配置)
    _token_b64 = "[API_TOKEN_BASE64]"
    token = base64.b64decode(_token_b64).decode("utf-8")

    # 使用 Gemini Pro
    model = ChatGoogleGenerativeAI(
        model="gemini-2.5-flash", api_key=SecretStr(token)
    )
    planner_llm = ChatGoogleGenerativeAI(
        model="gemini-2.5-pro", api_key=SecretStr(token)
    )

    # 或者使用 OpenAI via GitHub (如果上面 Gemini 部分被注释掉)
    # token_openai_github = "[GITHUB_PAT_TOKEN]" # 替换为你的 GitHub PAT
    # model = ChatOpenAI(
    #     base_url="https://******/inference",
    #     model="openai/gpt-4o",
    #     api_key=SecretStr(token_openai_github),
    # )
    # planner_llm = ChatOpenAI(
    #     base_url="https://******/inference",
    #     model="openai/gpt-4o",
    #     api_key=SecretStr(token_openai_github),
    # )
    
    message_context = (
        f"任务执行上下文信息：\n"
        f"1. 总体目标：分析10000个数据分析相关的职位描述\n"
        f"2. 当前进度：已完成{total_applications}个职位的分析\n"
        f"3. 剩余任务：还需分析{10000-total_applications}个职位\n"
        f"4. 完成度：{(total_applications/10000)*100:.1f}%\n"
        f"5. 重点关注：香港地区金融行业的合规、KYC、客服相关职位\n"
        f"6. 技术栈重点：数据分析、AI/ML、合规技术等相关技能\n"
        f"7. 输出要求：结构化数据收集，为思维导图创建做准备\n"
    )
    
    while True:
        logger.info("开始运行数据分析职位分析助手...")
        browser = Browser(
            config=BrowserConfig(
                browser_binary_path=(
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
                ),
                disable_security=True,
                headless=False,
            )
        )
        async with await browser.new_context() as context:
            try:
                agent = Agent(
                    task=ground_task,
                    message_context=message_context,
                    llm=model,
                    planner_llm=planner_llm,
                    use_vision_for_planner=True,
                    planner_interval=4,
                    controller=controller,
                    max_failures=3,
                    use_vision=True,
                    browser_context=context,
                    save_conversation_path="logs/find_apply_jobs",
                )
                agent.state = AgentState()
                history = await agent.run()
                if history:
                    logger.info(f"访问的URL数量: {len(history.urls())}")
                    # ... (其他日志) ...
                new_total_applications = total_applications
                if os.path.exists(APPLICATION_STATS_FILE):
                    try:
                        with open(
                            APPLICATION_STATS_FILE, "r", encoding="utf-8"
                        ) as f:
                            stats = json.load(f)
                            new_total_applications = stats.get(
                                "total_applications", 0
                            )
                    except Exception as e:
                        logger.error(f"读取“申请/分析”统计文件时出错: {e}")
                new_applications = (
                    new_total_applications - total_applications
                )
                logger.info(
                    f"本次运行新增了{new_applications}个职位分析，"
                    f"当前总计: {new_total_applications}/10000"
                )
                await asyncio.sleep(1)
                logger.info("\n等待1秒后继续下一轮...")
            except Exception as e:
                logger.error(f"agent运行失败，全部重置: {str(e)}")
                await asyncio.sleep(1)
                continue
        await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
\end{verbatim}
此部分详细描述了如何构建和组合给 大语言模型 的指令，以及智能体的初始化和主运行循环。

\subsection{运行演示脚本与成果展示}
\label{ssec:run_demo_showcase}
确保满足前提条件：仔细检查 Python 版本、已安装的库、Playwright 浏览器驱动程序、简历文件和 应用程序接口 密钥。

\subsubsection{配置 大语言模型}
在脚本中，确保所需的 大语言模型 配置已激活，并且 应用程序接口 密钥已正确设置。

\subsubsection{调整浏览器路径}
验证 \texttt{BrowserConfig} 中的 \texttt{browser\_binary\_path} 指向你本地的 Chrome 安装位置。

\subsubsection{执行脚本}
\begin{verbatim}
python your_script_name.py
\end{verbatim}
一个浏览器窗口应该会打开（因为 \texttt{headless=False}），你将看到智能体开始浏览网站、输入搜索查询，并根据提供的详尽提示提取职位信息。监控控制台输出和 \texttt{job\_search.log} 文件以了解进度和任何错误。

\subsubsection{运行结果与成果展示预期}
运行脚本后，你可以期待以下成果：
\begin{itemize}
    \item 自动生成的职位信息CSV文件，包含职位名称、公司、薪资等详细信息
    \item 结构化的技术元数据，可用于生成思维导图
    \item 详细的日志文件，记录整个搜索和分析过程
    \item 统计报告，显示已分析的职位数量和进度
    \item 可视化的浏览器操作过程，便于调试和优化
\end{itemize}

\section{核心概念解析}
\label{sec:core_concepts}
此演示脚本展示了构建人工智能驱动的浏览器智能体中的几个重要概念，这些概念体现了现代智能体动作系统的核心特征：

\begin{itemize}
    \item \textbf{多模态感知能力}：智能体通过结合视觉和文本信息理解网页内容，实现了类似人类的网页浏览体验。在求职场景中，智能体能够识别页面布局、理解职位描述文本、定位交互元素（如搜索框、按钮、文件上传区域等），并根据视觉线索做出相应的操作决策。这种多模态感知是智能体动作系统中感知模块的重要体现。

    \item \textbf{动态规划与适应性}：智能体根据任务进度和环境变化调整执行策略，体现了智能体动作系统的自适应特性。例如，当遇到不同招聘网站的页面结构时，智能体能够灵活调整搜索策略；当检测到已分析的职位数量时，能够动态调整后续的执行计划。这种动态规划能力使智能体能够在复杂多变的网络环境中保持高效运行。

    \item \textbf{鲁棒的错误处理机制}：智能体具备重试机制和异常恢复能力，确保在面对网络延迟、页面变更、元素识别失败等常见问题时能够自动恢复。脚本中实现的多层次错误处理策略包括：元素定位失败时的重试机制、页面加载异常时的重新启动流程、以及任务执行失败时的完整重置策略。这种容错能力是智能体动作系统稳定性的重要保障。

    \item \textbf{状态管理与记忆机制}：智能体维护任务进度和历史记录，通过文件系统持久化存储已分析的职位数量、申请统计信息等状态数据。这种状态管理能力使智能体能够在多次运行之间保持连续性，避免重复工作，并能够基于历史经验优化后续行为。

    \item \textbf{自定义动作扩展}：通过Controller注册特定的业务逻辑，实现了智能体动作系统的可扩展性。脚本中定义的自定义动作包括：\texttt{save\_jobs}（保存职位信息）、\texttt{read\_cv}（读取简历内容）、\texttt{upload\_cv}（上传简历文件）、\texttt{upload\_profile\_picture}（上传头像）等。这些自定义动作将通用的浏览器操作能力扩展为特定领域的专业功能。

    \item \textbf{数据持久化与结构化存储}：智能体将收集的信息保存到文件系统，实现了从非结构化网页内容到结构化数据的转换。通过CSV文件存储职位信息、JSON文件记录统计数据、日志文件追踪执行过程，智能体构建了完整的数据管理体系，为后续的数据分析和可视化提供了基础。

    \item \textbf{任务分解与执行策略}：智能体将复杂的求职任务分解为多个子任务：网站导航、关键词搜索、职位浏览、信息提取、数据存储等。每个子任务都有明确的执行策略和成功标准，体现了智能体动作系统中任务规划和执行的层次化特征。

    \item \textbf{上下文感知与个性化}：智能体能够根据用户的个人资料（如专业背景、目标地区、薪资期望等）调整搜索策略和筛选标准，实现了个性化的求职服务。这种上下文感知能力使智能体能够提供更加精准和相关的结果。

    \item \textbf{持续学习与优化}：通过记录执行历史、分析成功率、调整搜索关键词等机制，智能体具备了一定的学习能力，能够在多次执行中不断优化性能，提高任务完成的效率和质量。
\end{itemize}

\section{可扩展方向（定制化与进一步开发）}
\label{sec:extendable_directions}
该脚本提供了一个坚实的基础。以下是一些你可以定制或扩展它的方向：
\begin{itemize}
    \item \textbf{多平台支持}：扩展到更多招聘网站，如猎聘、BOSS直聘等
    \item \textbf{智能筛选}：基于个人偏好和技能匹配度进行职位筛选
    \item \textbf{自动申请}：在合适的职位上自动提交申请
    \item \textbf{数据分析}：对收集的职位数据进行深度分析和可视化
    \item \textbf{通知系统}：当发现匹配职位时发送邮件或消息通知
    \item \textbf{简历优化}：根据职位要求自动调整简历内容
    \item \textbf{面试准备}：基于职位要求生成面试问题和答案
\end{itemize}

\section{总结}
\label{sec:summary}
本项目示范了如何利用 \texttt{browser-use} 配合 LangChain（及其支持的多种大模型）构建一个自动化找工作助手，兼具实用性与强大的扩展性。它通过精心设计的提示和自定义动作，赋予了智能体浏览网页、理解内容、提取信息并执行特定任务（如保存数据、上传文件）的能力。

对于刚毕业的同学或希望系统性、高效寻找工作的开发者而言，这个项目不仅是一个可以直接使用的工具雏形，更是一个极佳的实战项目基础，可以帮助你深入理解和应用当前热门的智能体和浏览器自动化技术。通过不断的迭代和功能增强，你可以将其打造成一个高度个性化且功能强大的私人求职助理。

祝你自动化顺利，求职成功！
