\begin{figure}[h!t]
\centering
\begin{tikzpicture}[
    node distance=1.2cm and 1.8cm,
    every node/.style={font=\small},
    process/.style={rectangle, draw, minimum height=1cm, minimum width=2.8cm, align=center, rounded corners=3pt},
    decision/.style={diamond, draw, aspect=2, align=center, minimum width=2.5cm, minimum height=1.2cm},
    data/.style={cylinder, draw, shape border rotate=90, aspect=0.25, minimum height=1cm, minimum width=2.2cm, align=center},
    arrow/.style={->, thick},
    labelbox/.style={draw=none, align=center},
    bigbox/.style={rectangle, draw, thick, minimum height=10cm, minimum width=11cm, fill=gray!10, rounded corners=5pt},
    user/.style={circle, draw, minimum size=1.5cm, align=center, fill=blue!10}
]

% Main container for dialogue learning model - 扩展垂直高度
\node[bigbox, labelbox] (container) at (0,-3.0) {};

% User at the top - 提高位置
\node[user] (user) at (0,3.5) {用户};

% Information fusion - 增加垂直间距
\node[process, below=1.8cm of user] (fusion) {多模态信息获取、\\感知、融合};

% Intent classification - 增加垂直间距
\node[process, below=1.8cm of fusion] (intent) {意图分类};

% Decision diamond - 增加垂直间距
\node[decision, below=1.8cm of intent] (decision) {交互\\学习？};

% Knowledge extraction (right branch) - 保持水平位置
\node[process, right=3.5cm of intent] (extract) {知识抽取};

% New knowledge database - 增加垂直间距
\node[data, below=2.2cm of extract] (newknowledge) {新知识};

% Traditional dialogue model - 增加垂直间距
\node[process, below=2.8cm of decision] (dialogue) {传统对话管理模型};

% Corpus database - 增加垂直间距
\node[data, below=0.8cm of dialogue] (corpus) {语料库};

% Dashed box around traditional model and corpus
\draw[dashed, thick] ($(dialogue.north west)+(-0.3,0.3)$) rectangle ($(corpus.south east)+(0.3,-0.3)$);

% Main title of the container - 调整位置适应扩展的高度
\node[labelbox] at (0,-7.5) {\textbf{对话交互学习对话管理模型}};

% Arrows with labels
\draw[arrow] (user) -- (fusion);
\draw[arrow] (fusion) -- (intent);
\draw[arrow] (intent) -- (decision);
\draw[arrow] (decision.east) -- node[above, midway] {是} (extract.west);
\draw[arrow] (extract) -- (newknowledge);
\draw[arrow] (newknowledge.west) -- ++(-2.4,0) |- node[left, near end] {融合} (dialogue.east);
\draw[arrow] (corpus.north) -- (dialogue.south);
\draw[arrow] (decision.south) -- node[left, midway] {否} (dialogue.north);
\draw[arrow] (dialogue.west) -- ++(-2,0) |- ++(0,9.5) -- (user.west) node[midway, left] {反馈};
\draw[arrow] (fusion.east) -- ++(4,0) |- (extract.north);

\end{tikzpicture}
\caption{多模态对话智能体工作流程图}
\label{fig:ch1_mlmchat}
\end{figure}