#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent词汇替换和名词统一检查脚本
用于将第10章中的Agent替换为智能体，并检查名词统一问题
"""

import re
import sys
from typing import List, Tuple, Dict


def analyze_agent_usage(
    file_path: str,
) -> Tuple[List[Tuple[int, str, str]], List[Tuple[int, str, str]]]:
    """
    分析文件中Agent的使用情况
    返回: (需要替换的列表, 需要保留的列表)
    """
    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    # 需要保留的英文术语模式
    preserve_patterns = [
        r"\\textbf\{.*?Agents?\}",  # LaTeX加粗的英文术语
        r"Multi-Agent",  # Multi-Agent术语
        r"Human-Agent",  # Human-Agent术语
        r"Foundation Agents?",  # Foundation Agent术语
        r"Rule-Based Agents?",  # Rule-Based Agent术语
        r"Model-Based Agents?",  # Model-Based Agent术语
        r"model-based reflex agents?",  # model-based reflex agent术语
        r"evolutionary coding agent",  # evolutionary coding agent术语
        r"Adaptive and Self-Evolving Agents",  # 章节标题英文
    ]

    # 需要替换的Agent模式
    replace_patterns = [
        (r"Agent(?!s?\})", "智能体"),  # 单独的Agent，但不包括LaTeX加粗结尾
        (r"Agents(?!\})", "智能体"),  # 复数Agents，但不包括LaTeX加粗结尾
    ]

    to_replace = []
    to_preserve = []

    for line_num, line in enumerate(lines, 1):
        # 检查是否包含需要保留的模式
        preserve_match = False
        for pattern in preserve_patterns:
            if re.search(pattern, line):
                matches = re.finditer(pattern, line)
                for match in matches:
                    to_preserve.append(
                        (line_num, match.group(), "英文学术术语，需要保留")
                    )
                preserve_match = True

        # 如果不是需要保留的，检查是否需要替换
        if not preserve_match:
            for pattern, replacement in replace_patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    # 进一步检查上下文，确保不是英文术语的一部分
                    context = line[max(0, match.start() - 10) : match.end() + 10]
                    if not any(re.search(p, context) for p in preserve_patterns):
                        to_replace.append(
                            (line_num, match.group(), f"替换为{replacement}")
                        )

    return to_replace, to_preserve


def find_terminology_issues(file_path: str) -> Dict[str, List[Tuple[int, str]]]:
    """
    查找名词统一问题
    """
    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    issues = {
        "Agent/智能体混用": [],
        "多Agent vs 多智能体": [],
        "BDI Agent vs BDI智能体": [],
        "其他术语不一致": [],
    }

    for line_num, line in enumerate(lines, 1):
        # 检查Agent/智能体混用
        if "智能体" in line and re.search(r"Agent(?!s?\})", line):
            issues["Agent/智能体混用"].append((line_num, line.strip()))

        # 检查多Agent vs 多智能体
        if re.search(r"多Agent(?!s?\})", line):
            issues["多Agent vs 多智能体"].append((line_num, line.strip()))

        # 检查BDI Agent vs BDI智能体
        if re.search(r"BDI Agent(?!s?\})", line):
            issues["BDI Agent vs BDI智能体"].append((line_num, line.strip()))

        # 检查其他可能的术语不一致
        if (
            re.search(r"agent(?!s?\})", line, re.IGNORECASE)
            and "Agent" not in line
            and "智能体" not in line
        ):
            issues["其他术语不一致"].append((line_num, line.strip()))

    return issues


def perform_replacements(
    file_path: str, to_replace: List[Tuple[int, str, str]]
) -> None:
    """
    执行替换操作
    """
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 定义替换规则（按优先级排序）
    replacements = [
        # 保护英文术语（先标记）
        (r"\\textbf\{(.*?Agents?)\}", r"__PROTECT_\1__"),
        (r"Multi-Agent", "__PROTECT_Multi-Agent__"),
        (r"Human-Agent", "__PROTECT_Human-Agent__"),
        (r"Foundation Agents?", "__PROTECT_Foundation_Agent__"),
        (r"Rule-Based Agents?", "__PROTECT_Rule-Based_Agent__"),
        (r"Model-Based Agents?", "__PROTECT_Model-Based_Agent__"),
        (r"model-based reflex agents?", "__PROTECT_model-based_reflex_agent__"),
        (r"evolutionary coding agent", "__PROTECT_evolutionary_coding_agent__"),
        (
            r"Adaptive and Self-Evolving Agents",
            "__PROTECT_Adaptive_and_Self-Evolving_Agents__",
        ),
        # 执行替换
        (r"Agent(?!s)", "智能体"),
        (r"Agents", "智能体"),
        # 恢复保护的英文术语
        (r"__PROTECT_(.*?)__", r"\1"),
        (r"__PROTECT_\\textbf\{(.*?)\}__", r"\\textbf{\1}"),
    ]

    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)

    # 写回文件
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)


def main():
    file_path = "chapters/chapter10.tex"

    print("🔍 分析第10章Agent词汇使用情况...")
    to_replace, to_preserve = analyze_agent_usage(file_path)

    print(f"\n📊 分析结果:")
    print(f"需要替换的Agent: {len(to_replace)}处")
    print(f"需要保留的Agent: {len(to_preserve)}处")

    print(f"\n✅ 需要保留的英文术语:")
    for line_num, term, reason in to_preserve:
        print(f"  行{line_num}: {term} - {reason}")

    print(f"\n🔄 需要替换的Agent:")
    for line_num, term, replacement in to_replace[:10]:  # 只显示前10个
        print(f"  行{line_num}: {term} - {replacement}")
    if len(to_replace) > 10:
        print(f"  ... 还有{len(to_replace)-10}处需要替换")

    print(f"\n🔍 检查名词统一问题...")
    issues = find_terminology_issues(file_path)

    print(f"\n📋 名词统一问题报告:")
    for issue_type, issue_list in issues.items():
        if issue_list:
            print(f"\n❌ {issue_type}:")
            for line_num, line_content in issue_list:
                print(f"  行{line_num}: {line_content}")
        else:
            print(f"\n✅ {issue_type}: 无问题")

    # 询问是否执行替换
    response = input(f"\n是否执行替换操作? (y/N): ")
    if response.lower() == "y":
        print("🔄 执行替换操作...")
        perform_replacements(file_path, to_replace)
        print("✅ 替换完成!")

        # 重新检查
        print("\n🔍 重新检查名词统一问题...")
        issues_after = find_terminology_issues(file_path)
        print(f"\n📋 替换后的名词统一问题:")
        for issue_type, issue_list in issues_after.items():
            if issue_list:
                print(f"\n❌ {issue_type}:")
                for line_num, line_content in issue_list:
                    print(f"  行{line_num}: {line_content}")
            else:
                print(f"\n✅ {issue_type}: 无问题")
    else:
        print("❌ 取消替换操作")


if __name__ == "__main__":
    main()
