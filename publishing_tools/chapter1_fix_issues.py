#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第1章Agent替换后的问题修复脚本
"""

import re


def fix_chapter1_issues():
    """修复第1章替换后的问题"""
    file_path = "chapters/chapter1.tex"

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    print("🔧 修复第1章Agent替换后的问题...")

    # 修复规则
    fixes = [
        # 修复重复的智能体
        (r'"智能体"（智能体）', '"智能体"（Agent）'),
        (r"智能体（智能体）", "智能体（Agent）"),
        # 修复错误的替换
        (r"Intelligent 智能体s", "Intelligent Agents"),
        # 修复章节标题
        (r"\\chapter\{智能体基础概念\}", r"\\chapter{智能体基础概念}"),
        # 修复图表标题
        (r"\\caption\{智能体与环境交互示意图\}", r"\\caption{智能体与环境交互示意图}"),
        (
            r"\\caption\{主流智能体开发框架GitHub Star数量对比",
            r"\\caption{主流Agent开发框架GitHub Star数量对比",
        ),
        # 修复特定的专有名词
        (r"智能体 Development Kit", "Agent Development Kit"),
        # 修复注释中的问题
        (
            r"% 1\.1 智能体的概念，讨论什么是agent",
            "% 1.1 智能体的概念，讨论什么是智能体",
        ),
        (
            r"agent与LLM有什么区别，agent与wrokflows的区别",
            "智能体与LLM有什么区别，智能体与workflows的区别",
        ),
        (
            r"% 1\.2 【理论】智能体的发展历史.*?智能体元年",
            "% 1.2 【理论】智能体的发展历史，解答一些大家比较疑惑的问题，为什么李飞飞说2025年会是Agent元年",
        ),
        (r"% 1\.3 【实践】智能体 =", "% 1.3 【实践】Agent ="),
        # 修复其他注释
        (r"% 如下图为agent示意图", "% 如下图为智能体示意图"),
        (r"%   \\caption\{BibTeX词条生成agent", "%   \\caption{BibTeX词条生成智能体"),
        # 修复混合使用的问题
        (
            r"我们打开的Gemini、ChatGPT、DeepSeek的界面是agent，而只有其API才是LLM，LLM本质上是软件，是agent的大脑",
            "我们打开的Gemini、ChatGPT、DeepSeek的界面是智能体，而只有其API才是LLM，LLM本质上是软件，是智能体的大脑",
        ),
    ]

    new_content = content
    for pattern, replacement in fixes:
        new_content = re.sub(pattern, replacement, new_content)

    # 写回文件
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(new_content)

    print("✅ 第1章问题修复完成!")


def verify_chapter1_fixes():
    """验证第1章修复结果"""
    file_path = "chapters/chapter1.tex"

    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    print("\n🔍 验证第1章修复结果:")
    print("=" * 50)

    issues_found = []

    for line_num, line in enumerate(lines, 1):
        # 检查重复的智能体
        if '"智能体"（智能体）' in line or "智能体（智能体）" in line:
            issues_found.append((line_num, "重复智能体", line.strip()))

        # 检查错误的替换
        if "Intelligent 智能体s" in line:
            issues_found.append((line_num, "错误替换", line.strip()))

        # 检查小写agent
        if (
            re.search(r"\bagent\b", line, re.IGNORECASE)
            and "Agent" not in line
            and "智能体" not in line
        ):
            issues_found.append((line_num, "小写agent", line.strip()))

    if issues_found:
        print("❌ 发现以下问题:")
        for line_num, issue_type, content in issues_found:
            print(f"  行{line_num} ({issue_type}): {content}")
    else:
        print("✅ 所有问题已修复!")

    # 统计最终结果
    agent_count = sum(1 for line in lines if "Agent" in line)
    zhinen_count = sum(1 for line in lines if "智能体" in line)

    print(f"\n📊 最终统计:")
    print(f"包含Agent的行数: {agent_count}")
    print(f"包含智能体的行数: {zhinen_count}")


if __name__ == "__main__":
    fix_chapter1_issues()
    verify_chapter1_fixes()
