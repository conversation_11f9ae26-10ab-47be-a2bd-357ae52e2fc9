#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent术语统一工具 - intro.tex专用
将中文语境中的Agent替换为智能体，保留英文学术术语中的Agent
"""

import re
import os


def analyze_and_replace_agent_terms(file_path):
    """分析并替换Agent术语"""

    # 读取文件内容
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 保存原始内容用于对比
    original_content = content

    # 需要保留的英文术语模式（不替换）
    preserve_patterns = [r"multi-Agent", r"Multi-Agent", r"multi-agent", r"Multi-agent"]

    # 临时标记保留的术语
    preserved_terms = {}
    temp_marker = "PRESERVE_AGENT_TERM_"

    for i, pattern in enumerate(preserve_patterns):
        marker = f"{temp_marker}{i}"
        matches = re.findall(pattern, content)
        if matches:
            preserved_terms[marker] = matches[0]
            content = re.sub(pattern, marker, content)

    # 替换中文语境中的Agent
    replacements = [
        # 基本替换
        (r"Agent技术", "智能体技术"),
        (r"Agent（智能体）", "智能体"),
        (r"Agent的", "智能体的"),
        (r"Agent提供", "智能体提供"),
        (r"Agent通过", "智能体通过"),
        (r"Agent系统", "智能体系统"),
        (r"Agent领域", "智能体领域"),
        (r"Agent记忆", "智能体记忆"),
        (r"Agent优化", "智能体优化"),
        (r"Agent场景", "智能体场景"),
        (r"Agent涉及", "智能体涉及"),
        (r"构建Agent", "构建智能体"),
        (r"开发Agent", "开发智能体"),
        (r"理解 Agent", "理解智能体"),
        (r"建立 Agent", "建立智能体"),
        (r"对Agent", "对智能体"),
        (r"AI Agent", "AI智能体"),
        (r"自适应Agent", "自适应智能体"),
        (r"下一代Agent", "下一代智能体"),
        (r"新一代Agent", "新一代智能体"),
        (r"更智能的Agent", "更智能的智能体"),
        (r"赋予了Agent", "赋予了智能体"),
        (r"为Agent", "为智能体"),
        (r"支持Agent", "支持智能体"),
        (r"驱动Agent", "驱动智能体"),
        (r"剖析Agent", "剖析智能体"),
        (r"智能行动者（Agent）", "智能行动者"),
        # 特殊情况处理
        (r"Agent\s*\n", "智能体\n"),
        (r"Agent\s*。", "智能体。"),
        (r"Agent\s*，", "智能体，"),
        (r"Agent\s*：", "智能体："),
    ]

    # 执行替换
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)

    # 删除不再需要的脚注
    footnote_pattern = r'\\footnote\{为简化叙述，本文以下统一以英文agent指代"智能体"，二者语义等同，特此说明。\}'
    content = re.sub(footnote_pattern, "", content)

    # 恢复保留的术语
    for marker, term in preserved_terms.items():
        content = content.replace(marker, term)

    # 写回文件
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)

    # 生成报告
    return generate_report(original_content, content, file_path)


def generate_report(original_content, new_content, file_path):
    """生成替换报告"""

    original_lines = original_content.split("\n")
    new_lines = new_content.split("\n")

    changes = []

    for i, (orig_line, new_line) in enumerate(zip(original_lines, new_lines), 1):
        if orig_line != new_line:
            changes.append(
                {"line": i, "original": orig_line.strip(), "new": new_line.strip()}
            )

    # 统计Agent出现次数
    original_agent_count = len(re.findall(r"Agent", original_content))
    new_agent_count = len(re.findall(r"Agent", new_content))

    report = f"""
# Agent术语统一报告 - {os.path.basename(file_path)}

## 📊 统计信息
- 原始Agent出现次数: {original_agent_count}
- 替换后Agent出现次数: {new_agent_count}
- 成功替换次数: {original_agent_count - new_agent_count}

## 📝 详细变更记录

"""

    for change in changes:
        report += f"**第{change['line']}行:**\n"
        report += f"- 原文: {change['original']}\n"
        report += f"- 修改: {change['new']}\n\n"

    # 检查剩余的Agent术语
    remaining_agents = re.findall(r"Agent\w*", new_content)
    if remaining_agents:
        report += "## ⚠️ 剩余的Agent术语\n"
        for agent in set(remaining_agents):
            count = new_content.count(agent)
            report += f"- `{agent}`: {count}次\n"

    return report


def main():
    """主函数"""
    file_path = "chapters/intro.tex"

    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return

    print(f"正在处理文件: {file_path}")

    try:
        report = analyze_and_replace_agent_terms(file_path)

        # 保存报告
        report_path = "publishing_tools/agent_terminology_report_intro.md"
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report)

        print(f"✅ 处理完成!")
        print(f"📄 报告已保存到: {report_path}")
        print("\n" + "=" * 50)
        print(report)

    except Exception as e:
        print(f"❌ 处理失败: {e}")


if __name__ == "__main__":
    main()
