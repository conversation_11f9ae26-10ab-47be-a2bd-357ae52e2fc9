# 出版工具集 (Publishing Tools)

本文件夹包含用于满足出版社文字规范要求的专用工具脚本，主要用于术语统一、文本规范化和质量检查。

## 📁 文件说明

### 核心工具脚本

1. **`agent_analysis.py`** - Agent词汇分析和替换工具
   - 功能：分析文档中Agent的使用情况，执行智能替换
   - 用途：将中文语境中的"Agent"替换为"智能体"，保留英文学术术语
   - 使用：`python3 agent_analysis.py`

2. **`replace_agent_terms.py`** - Agent术语替换脚本（高级版）
   - 功能：更复杂的Agent词汇替换和名词统一检查
   - 特点：支持交互式操作，提供详细的分析报告
   - 使用：`python3 replace_agent_terms.py`

### 文档报告

3. **`agent_terminology_report.md`** - Agent词汇替换与名词统一报告
   - 内容：第10章Agent替换工作的完整报告
   - 包含：替换统计、保留术语清单、质量保证记录
   - 用途：作为工作记录和质量验证依据

## 🎯 工具用途

### 主要功能
- **术语统一**：确保中文语境使用"智能体"，英文术语保留"Agent"
- **格式保护**：保护LaTeX格式和学术术语标记
- **质量检查**：自动检测和报告术语使用问题
- **批量处理**：高效处理大量文本内容

### 适用场景
- 学术书籍出版前的术语规范化
- 技术文档的中英文术语统一
- LaTeX文档的格式保护和内容处理
- 多章节文档的批量术语检查

## 🚀 使用指南

### 基本使用流程

1. **分析阶段**
   ```bash
   cd /path/to/agent_book/publishing_tools
   python3 agent_analysis.py
   ```

2. **查看报告**
   ```bash
   cat agent_terminology_report.md
   ```

3. **处理其他章节**
   - 修改脚本中的文件路径
   - 运行相应的处理脚本
   - 生成新的报告文档

### 自定义配置

在使用脚本处理其他章节时，需要修改以下配置：

```python
# 在脚本中修改目标文件路径
file_path = "chapters/chapter_XX.tex"  # 替换为目标章节

# 根据需要调整保留的术语模式
preserve_patterns = [
    r'\\textbf\{.*?Agents?\}',
    r'Multi-Agent',
    # 添加其他需要保留的术语模式
]
```

## 📋 术语规范

### 替换规则
- **中文语境**: Agent → 智能体
- **英文学术术语**: 保持Agent不变
- **LaTeX加粗术语**: 保持原有格式
- **专有名词**: 如Agentic AI保持不变

### 保留模式
- `\textbf{...Agent...}` - LaTeX加粗的学术术语
- `Multi-Agent` - 多智能体系统术语
- `Human-Agent` - 人机协同术语
- `Foundation Agents` - 基础智能体术语
- `evolutionary coding agent` - 专有技术名词

## 🔧 扩展开发

### 添加新的术语处理

1. **扩展保留模式**
   ```python
   preserve_patterns.append(r'新的术语模式')
   ```

2. **添加新的替换规则**
   ```python
   replacements.append((r'原术语', '新术语'))
   ```

3. **增加质量检查**
   ```python
   issues["新问题类型"] = []
   ```

### 处理其他术语

这些工具可以作为模板，用于处理其他需要统一的术语：
- AI/人工智能
- Machine Learning/机器学习
- Deep Learning/深度学习
- 等等...

## 📊 质量保证

### 验证步骤
1. **自动检测**: 脚本自动识别问题
2. **手动审查**: 人工验证关键术语
3. **格式检查**: 确保LaTeX格式完整
4. **最终确认**: 生成详细报告

### 输出报告
每次处理后都会生成包含以下内容的报告：
- 替换统计数据
- 保留术语清单
- 发现的问题列表
- 质量验证结果

## 📝 使用记录

### 已处理章节
- ✅ 第10章 (chapters/chapter10.tex) - Agent词汇统一完成

### 待处理章节
- ⏳ 其他章节根据需要进行处理

## 🤝 贡献指南

如需扩展或改进这些工具：
1. 保持代码的可读性和注释完整性
2. 遵循现有的命名规范和文件结构
3. 更新README文档说明新功能
4. 提供使用示例和测试用例

---

**注意**: 使用这些工具前请备份原始文件，确保可以在需要时恢复原始内容。
