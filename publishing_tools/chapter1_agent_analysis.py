#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第1章Agent词汇分析和替换脚本
专门处理第1章的Agent术语统一问题
"""

import re
import sys
from typing import List, Tuple, Dict

def analyze_chapter1_agents() -> None:
    """分析第1章中的Agent使用情况"""
    file_path = "chapters/chapter1.tex"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    print("🔍 第1章Agent词汇分析报告")
    print("=" * 60)
    
    # 需要保留的英文术语模式
    preserve_patterns = [
        r'\\textbf\{.*?Agents?\}',  # LaTeX加粗的英文术语
        r'Multi-Agent',             # Multi-Agent术语
        r'Human-Agent',             # Human-Agent术语
        r'Travel Agent',            # 旅行代理
        r'Real Estate Agent',       # 地产经纪人
        r'AI Agent',                # AI Agent专有名词
        r'Agent Development Kit',   # Google ADK
        r'Agent开发框架',           # 框架名称中的Agent
        r'GitHub.*Agent',           # GitHub相关的Agent项目
        r'AutoGen',                 # 微软AutoGen框架
        r'LangGraph',               # LangChain的LangGraph
        r'Agentic Systems',         # 智能体系统
        r'Agentic',                 # Agentic相关术语
    ]
    
    # 分析每一行
    to_preserve = []
    to_replace = []
    
    for line_num, line in enumerate(lines, 1):
        if 'Agent' in line:
            # 检查是否包含需要保留的模式
            preserve_this_line = False
            for pattern in preserve_patterns:
                if re.search(pattern, line):
                    preserve_this_line = True
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        to_preserve.append((line_num, match.group(), "英文学术术语/专有名词"))
                    break
            
            if not preserve_this_line:
                # 查找需要替换的Agent
                agent_matches = re.finditer(r'Agent(?!s?\})', line)
                for match in agent_matches:
                    to_replace.append((line_num, match.group(), "替换为智能体"))
    
    print(f"📊 统计结果:")
    print(f"总计包含Agent的行数: {len([l for l in lines if 'Agent' in l])}")
    print(f"需要保留的Agent术语: {len(to_preserve)}处")
    print(f"需要替换的Agent: {len(to_replace)}处")
    
    print(f"\n✅ 需要保留的英文术语/专有名词:")
    for line_num, term, reason in to_preserve:
        print(f"  行{line_num}: {term} ({reason})")
    
    print(f"\n🔄 需要替换的Agent:")
    for line_num, term, replacement in to_replace:
        print(f"  行{line_num}: {term} -> {replacement}")
    
    return to_replace, to_preserve

def check_terminology_issues() -> Dict[str, List[Tuple[int, str]]]:
    """检查第1章的名词统一问题"""
    file_path = "chapters/chapter1.tex"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"\n🔍 第1章名词统一问题检查:")
    print("=" * 60)
    
    issues = {
        "Agent/智能体混用": [],
        "章节标题问题": [],
        "注释中的Agent": [],
        "图表标题中的Agent": [],
        "专有名词不一致": [],
        "其他术语问题": []
    }
    
    for line_num, line in enumerate(lines, 1):
        # 检查Agent/智能体混用
        if '智能体' in line and re.search(r'Agent(?!s?\})', line):
            # 排除保留的英文术语
            preserve_patterns = [
                r'Travel Agent', r'Real Estate Agent', r'AI Agent',
                r'Agent Development Kit', r'AutoGen', r'Agentic'
            ]
            is_preserved = any(re.search(p, line) for p in preserve_patterns)
            if not is_preserved:
                issues["Agent/智能体混用"].append((line_num, line.strip()))
        
        # 检查章节标题
        if line.startswith(r'\chapter{') and 'Agent' in line:
            issues["章节标题问题"].append((line_num, line.strip()))
        
        # 检查注释中的Agent
        if line.strip().startswith('%') and 'Agent' in line:
            issues["注释中的Agent"].append((line_num, line.strip()))
        
        # 检查图表标题
        if (r'\caption{' in line or r'\label{' in line) and 'Agent' in line:
            issues["图表标题中的Agent"].append((line_num, line.strip()))
        
        # 检查专有名词不一致
        if re.search(r'agent(?!s?\})', line, re.IGNORECASE) and 'Agent' not in line and '智能体' not in line:
            issues["专有名词不一致"].append((line_num, line.strip()))
    
    for issue_type, issue_list in issues.items():
        if issue_list:
            print(f"\n❌ {issue_type}:")
            for line_num, line_content in issue_list:
                print(f"  行{line_num}: {line_content}")
        else:
            print(f"\n✅ {issue_type}: 无问题")
    
    return issues

def perform_chapter1_replacements() -> None:
    """执行第1章的Agent替换操作"""
    file_path = "chapters/chapter1.tex"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"\n🔄 执行第1章Agent替换操作...")
    
    # 替换规则（按优先级排序）
    replacements = [
        # 先保护需要保留的英文术语
        (r'(Travel Agent)', r'__PROTECT__\1__PROTECT__'),
        (r'(Real Estate Agent)', r'__PROTECT__\1__PROTECT__'),
        (r'(AI Agent)', r'__PROTECT__\1__PROTECT__'),
        (r'(Agent Development Kit)', r'__PROTECT__\1__PROTECT__'),
        (r'(AutoGen)', r'__PROTECT__\1__PROTECT__'),
        (r'(LangGraph)', r'__PROTECT__\1__PROTECT__'),
        (r'(Agentic Systems?)', r'__PROTECT__\1__PROTECT__'),
        (r'(Agentic)', r'__PROTECT__\1__PROTECT__'),
        (r'(GitHub.*?Agent.*?框架)', r'__PROTECT__\1__PROTECT__'),
        
        # 保护章节标题中的Agent（暂时保留英文）
        (r'(\\chapter\{Agent基础概念\})', r'__PROTECT__\1__PROTECT__'),
        
        # 保护图表标题中的Agent
        (r'(\\caption\{.*?Agent.*?\})', r'__PROTECT__\1__PROTECT__'),
        (r'(\\label\{.*?agent.*?\})', r'__PROTECT__\1__PROTECT__'),
        
        # 执行替换（中文语境中的Agent）
        (r'Agent(?!s?\})', '智能体'),
        
        # 恢复保护的术语
        (r'__PROTECT__(.*?)__PROTECT__', r'\1'),
    ]
    
    new_content = content
    for pattern, replacement in replacements:
        new_content = re.sub(pattern, replacement, new_content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ 第1章Agent替换完成!")

def generate_chapter1_report() -> None:
    """生成第1章处理报告"""
    print(f"\n📋 第1章Agent词汇处理任务清单:")
    print("=" * 60)
    
    print("🎯 优先级1：Agent词汇分析和替换")
    print("  1. ✅ 分析第1章Agent使用情况")
    print("  2. ✅ 区分保留和替换的Agent")
    print("  3. 🔄 执行智能替换")
    print("  4. ⏳ 验证替换结果")
    
    print("\n🎯 优先级2：名词统一问题检查")
    print("  1. ✅ 术语一致性检查")
    print("  2. ✅ 格式规范检查")
    print("  3. ✅ 生成问题报告")
    print("  4. ⏳ 质量验证")

def main():
    """主函数"""
    print("🚀 第1章Agent词汇统一处理工具")
    print("=" * 60)
    
    # 生成任务清单
    generate_chapter1_report()
    
    # 分析Agent使用情况
    to_replace, to_preserve = analyze_chapter1_agents()
    
    # 检查名词统一问题
    issues = check_terminology_issues()
    
    # 询问是否执行替换
    response = input(f"\n是否执行第1章的Agent替换操作? (y/N): ")
    if response.lower() == 'y':
        perform_chapter1_replacements()
        
        # 重新检查
        print("\n🔍 重新检查第1章处理结果...")
        issues_after = check_terminology_issues()
        
        print(f"\n🎉 第1章Agent词汇统一处理完成!")
        print("✅ 中文语境中的Agent已替换为智能体")
        print("✅ 英文学术术语中的Agent得到保留")
        print("✅ 实现了术语的统一和规范化")
    else:
        print("❌ 取消替换操作")

if __name__ == "__main__":
    main()
