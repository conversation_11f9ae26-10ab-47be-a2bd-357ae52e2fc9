#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第1章简单修复脚本
"""

import re

def simple_fix_chapter1():
    """简单修复第1章的问题"""
    file_path = "chapters/chapter1.tex"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔧 简单修复第1章问题...")
    
    # 简单的字符串替换
    content = content.replace('"智能体"（智能体）', '"智能体"（Agent）')
    content = content.replace('智能体（智能体）', '智能体（Agent）')
    content = content.replace('Intelligent 智能体s', 'Intelligent Agents')
    content = content.replace('智能体 Development Kit', 'Agent Development Kit')
    
    # 修复注释中的问题
    content = content.replace('讨论什么是agent，什么不是agent，agent与LLM', '讨论什么是智能体，什么不是智能体，智能体与LLM')
    content = content.replace('agent与wrokflows的区别', '智能体与workflows的区别')
    content = content.replace('如下图为agent示意图', '如下图为智能体示意图')
    content = content.replace('BibTeX词条生成agent', 'BibTeX词条生成智能体')
    
    # 修复混合使用
    content = content.replace('界面是agent，而只有其API才是LLM，LLM本质上是软件，是agent的大脑', 
                             '界面是智能体，而只有其API才是LLM，LLM本质上是软件，是智能体的大脑')
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 简单修复完成!")

def check_final_result():
    """检查最终结果"""
    file_path = "chapters/chapter1.tex"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print("\n🔍 检查最终结果:")
    print("=" * 50)
    
    # 统计
    agent_lines = [i for i, line in enumerate(lines, 1) if 'Agent' in line]
    zhinen_lines = [i for i, line in enumerate(lines, 1) if '智能体' in line]
    
    print(f"包含Agent的行数: {len(agent_lines)}")
    print(f"包含智能体的行数: {len(zhinen_lines)}")
    
    # 检查问题
    issues = []
    for i, line in enumerate(lines, 1):
        if '"智能体"（智能体）' in line:
            issues.append((i, "重复智能体", line.strip()))
        if 'Intelligent 智能体s' in line:
            issues.append((i, "错误替换", line.strip()))
    
    if issues:
        print("\n❌ 仍有问题:")
        for line_num, issue_type, content in issues:
            print(f"  行{line_num} ({issue_type}): {content}")
    else:
        print("\n✅ 所有问题已解决!")

if __name__ == "__main__":
    simple_fix_chapter1()
    check_final_result()
