#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent词汇分析和替换脚本
"""

import re

def analyze_and_replace():
    file_path = "chapters/chapter10.tex"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
    
    print("🔍 分析第10章中的Agent使用情况:")
    print("=" * 60)
    
    # 需要保留的英文术语（在LaTeX textbf中或特定术语）
    preserve_patterns = [
        r'\\textbf\{.*?Agents?\}',  # LaTeX加粗的英文术语
        r'Multi-Agent',             # Multi-Agent术语
        r'Human-Agent',             # Human-Agent术语
        r'Foundation Agents?',      # Foundation Agent术语
        r'Rule-Based Agents?',      # Rule-Based Agent术语
        r'Model-Based Agents?',     # Model-Based Agent术语
        r'model-based reflex agents?',  # model-based reflex agent术语
        r'evolutionary coding agent',   # evolutionary coding agent术语
        r'Adaptive and Self-Evolving Agents',  # 章节标题英文
    ]
    
    # 分析每一行
    to_preserve = []
    to_replace = []
    
    for line_num, line in enumerate(lines, 1):
        if 'Agent' in line:
            # 检查是否包含需要保留的模式
            preserve_this_line = False
            for pattern in preserve_patterns:
                if re.search(pattern, line):
                    preserve_this_line = True
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        to_preserve.append((line_num, match.group(), "英文学术术语"))
                    break
            
            if not preserve_this_line:
                # 查找需要替换的Agent
                agent_matches = re.finditer(r'Agent(?!s?\})', line)
                for match in agent_matches:
                    to_replace.append((line_num, match.group(), "替换为智能体"))
    
    print(f"📊 统计结果:")
    print(f"需要保留的Agent术语: {len(to_preserve)}处")
    print(f"需要替换的Agent: {len(to_replace)}处")
    
    print(f"\n✅ 需要保留的英文术语:")
    for line_num, term, reason in to_preserve:
        print(f"  行{line_num}: {term} ({reason})")
    
    print(f"\n🔄 需要替换的Agent:")
    for line_num, term, replacement in to_replace:
        print(f"  行{line_num}: {term} -> {replacement}")
    
    # 检查名词统一问题
    print(f"\n🔍 名词统一问题检查:")
    print("=" * 60)
    
    issues = {
        "Agent/智能体混用": [],
        "多Agent术语": [],
        "其他不一致": []
    }
    
    for line_num, line in enumerate(lines, 1):
        # 检查Agent/智能体混用
        if '智能体' in line and re.search(r'Agent(?!s?\})', line):
            # 排除保留的英文术语
            is_preserved = any(re.search(p, line) for p in preserve_patterns)
            if not is_preserved:
                issues["Agent/智能体混用"].append((line_num, line.strip()))
        
        # 检查多Agent术语
        if re.search(r'多Agent(?!s?\})', line):
            issues["多Agent术语"].append((line_num, line.strip()))
    
    for issue_type, issue_list in issues.items():
        if issue_list:
            print(f"\n❌ {issue_type}:")
            for line_num, line_content in issue_list:
                print(f"  行{line_num}: {line_content}")
        else:
            print(f"\n✅ {issue_type}: 无问题")
    
    # 执行替换
    print(f"\n🔄 执行替换操作...")
    
    # 替换规则
    replacements = [
        # 先保护需要保留的英文术语
        (r'(\\textbf\{.*?Agents?\})', r'__PROTECT__\1__PROTECT__'),
        (r'(Multi-Agent)', r'__PROTECT__\1__PROTECT__'),
        (r'(Human-Agent)', r'__PROTECT__\1__PROTECT__'),
        (r'(Foundation Agents?)', r'__PROTECT__\1__PROTECT__'),
        (r'(Rule-Based Agents?)', r'__PROTECT__\1__PROTECT__'),
        (r'(Model-Based Agents?)', r'__PROTECT__\1__PROTECT__'),
        (r'(model-based reflex agents?)', r'__PROTECT__\1__PROTECT__'),
        (r'(evolutionary coding agent)', r'__PROTECT__\1__PROTECT__'),
        (r'(Adaptive and Self-Evolving Agents)', r'__PROTECT__\1__PROTECT__'),
        
        # 替换普通的Agent
        (r'Agent(?!s?\})', '智能体'),
        
        # 恢复保护的术语
        (r'__PROTECT__(.*?)__PROTECT__', r'\1'),
    ]
    
    new_content = content
    for pattern, replacement in replacements:
        new_content = re.sub(pattern, replacement, new_content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ 替换完成!")
    
    # 验证替换结果
    print(f"\n🔍 验证替换结果...")
    with open(file_path, 'r', encoding='utf-8') as f:
        new_lines = f.readlines()
    
    remaining_agents = []
    for line_num, line in enumerate(new_lines, 1):
        if re.search(r'Agent(?!s?\})', line):
            # 检查是否是保留的术语
            is_preserved = any(re.search(p, line) for p in preserve_patterns)
            if not is_preserved:
                remaining_agents.append((line_num, line.strip()))
    
    if remaining_agents:
        print(f"❌ 仍有{len(remaining_agents)}处Agent未替换:")
        for line_num, line_content in remaining_agents:
            print(f"  行{line_num}: {line_content}")
    else:
        print("✅ 所有需要替换的Agent都已成功替换为智能体!")

if __name__ == "__main__":
    analyze_and_replace()
