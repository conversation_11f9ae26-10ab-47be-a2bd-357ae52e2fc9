# 智能体系统技术全书 - 参考文献资源

本文档整理了《智能体系统技术全书》中引用的所有参考文献和资源链接，供读者深入学习和研究使用。

## 📚 学术论文

### 基础理论与概念

1. **<PERSON><PERSON><PERSON>, <PERSON>, & <PERSON>, N. <PERSON> (1995)**  
   *Agent theories, architectures, and languages: A survey*  
   Intelligent agents, 1-39. Springer.

2. **<PERSON>, <PERSON>, & <PERSON>, P. (2020)**  
   *Artificial Intelligence: A Modern Approach* (3rd edition)  
   Pearson, Hoboken, NJ. ISBN: 978-0134610993

3. **<PERSON>, P. <PERSON> (1992)**  
   *Artificial intelligence*  
   Addison-Wesley Longman Publishing Co., Inc.

### 大语言模型与神经网络

4. **<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2003)**  
   *A neural probabilistic language model*  
   Journal of Machine Learning Research, 3, 1137-1155.

5. **<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, G. S., & <PERSON>, J. (2013)**  
   *Distributed representations of words and phrases and their compositionality*  
   Advances in neural information processing systems, 3111-3119.

6. **<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, J. (2016)**  
   *Deep Residual Learning for Image Recognition*  
   Proceedings of the IEEE conference on computer vision and pattern recognition, 770-778.

### 推理与规划

7. **Wei, J., Wang, X., Schuurmans, D., Bosma, M., Xia, F., Chi, E., Le, Q. V., Zhou, D., et al. (2022)**  
   *Chain-of-thought prompting elicits reasoning in large language models*  
   Advances in neural information processing systems, 35, 24824-24837.

8. **Yao, S., Zhao, J., Yu, D., Du, N., Shafran, I., Narasimhan, K., & Cao, Y. (2023)**  
   *React: Synergizing reasoning and acting in language models*  
   International Conference on Learning Representations (ICLR).

9. **Yao, S., Yu, D., Zhao, J., Shafran, I., Griffiths, T., Cao, Y., & Narasimhan, K. (2023)**  
   *Tree of thoughts: Deliberate problem solving with large language models*  
   Advances in neural information processing systems, 36, 11809-11822.

### 代码生成与程序合成

10. **Austin, J., Odena, A., Nye, M., Bosma, M., Michalewski, H., Dohan, D., Jiang, E., Cai, C., Terry, M., Le, Q., et al. (2021)**  
    *Program synthesis with large language models*  
    arXiv preprint arXiv:2108.07732.

11. **Chen, M., Tworek, J., Jun, H., Yuan, Q., Pinto, H. P. D. O., Kaplan, J., Edwards, H., Burda, Y., Joseph, N., Brockman, G., et al. (2021)**  
    *Evaluating large language models trained on code*  
    arXiv preprint arXiv:2107.03374.

12. **Wang, Z., Zhou, S., Fried, D., & Neubig, G. (2022)**  
    *Execution-based evaluation for open-domain code generation*  
    arXiv preprint arXiv:2212.10481.

13. **Lai, Y., Li, C., Wang, Y., Zhang, T., Zhong, R., Zettlemoyer, L., Yih, W., Fried, D., Wang, S., & Yu, T. (2023)**  
    *DS-1000: A natural and reliable benchmark for data science code generation*  
    International Conference on Machine Learning, 18319-18345. PMLR.

### 智能体评估与基准测试

14. **Liu, X., Yu, H., Zhang, H., Xu, Y., Lei, X., Lai, H., Gu, Y., Ding, H., Men, K., Yang, K., et al. (2023)**  
    *Agentbench: Evaluating llms as agents*  
    arXiv preprint arXiv:2308.03688.

15. **Sun, J., Hua, Z., & Xia, Y. (2025)**  
    *Autoeval: A practical framework for autonomous evaluation of mobile agents*  
    arXiv preprint arXiv:2503.02403.

16. **Valmeekam, K., Marquez, M., Olmo, A., Sreedharan, S., & Kambhampati, S. (2023)**  
    *Planbench: An extensible benchmark for evaluating large language models on planning and reasoning about change*  
    Advances in Neural Information Processing Systems, 36, 38975-38987.

### 工具使用与API集成

17. **Li, M., Zhao, Y., Yu, B., Song, F., Li, H., Yu, H., Li, Z., Huang, F., & Li, Y. (2023)**  
    *Api-bank: A comprehensive benchmark for tool-augmented llms*  
    arXiv preprint arXiv:2304.08244.

18. **Wang, X., Wang, Z., Liu, J., Chen, Y., Yuan, L., Peng, H., & Ji, H. (2023)**  
    *Mint: Evaluating llms in multi-turn interaction with tools and language feedback*  
    arXiv preprint arXiv:2309.10691.

### 前沿研究

19. **Liu, B., Li, X., Zhang, J., Wang, J., He, T., Hong, S., Liu, H., Zhang, S., Song, K., Zhu, K., et al. (2025)**  
    *Advances and Challenges in Foundation Agents: From Brain-Inspired Intelligence to Evolutionary, Collaborative, and Safe Systems*  
    arXiv preprint arXiv:2504.01990.

20. **Morris, M. R., Sohl-Dickstein, J., Fiedel, N., Warkentin, T., Dafoe, A., Faust, A., Farabet, C., & Legg, S. (2024)**  
    *Position: Levels of AGI for operationalizing progress on the path to AGI*  
    Forty-first International Conference on Machine Learning.

## 🌐 在线资源与博客文章

### 行业观点与趋势

21. **Gates, B. (2023, March 22)**  
    *The Age of AI Has Begun*  
    https://www.gatesnotes.com/the-age-of-ai-has-begun

22. **Karpathy, A. (2025)**  
    *Software is Changing*  
    YouTube视频: https://www.youtube.com/watch?v=LCEmiRjPEtQ

### 公司技术博客与指南

23. **OpenAI (2025, February 2)**  
    *Introducing Deep Research*  
    https://openai.com/index/introducing-deep-research/

24. **OpenAI (2025, January 23)**  
    *Introducing Operator*  
    https://openai.com/index/introducing-operator/

25. **OpenAI (2024)**  
    *A Practical Guide to Building Agents*  
    https://cdn.openai.com/business-guides-and-resources/a-practical-guide-to-building-agents.pdf

26. **Anthropic (2024, November 25)**  
    *Introducing the Model Context Protocol*  
    https://www.anthropic.com/news/model-context-protocol

27. **Schluntz, E., & Zhang, B. (2024, December 19)**  
    *Building effective agents*  
    Anthropic工程博客: https://www.anthropic.com/engineering/building-effective-agents

### 新闻报道与分析

28. **Bloomberg (2024, July 12)**  
    *OpenAI Scale Ranks Progress Toward 'Human-Level' Problem Solving*  
    https://www.bloomberg.com/news/articles/2024-07-11/openai-sets-levels-to-track-progress-toward-superintelligent-ai

### 白皮书与技术报告

29. **PPC (2024, January)**  
    *Agents2: New White Paper on Agent Technology*  
    https://ppc.land/content/files/2025/01/Newwhitepaper_Agents2.pdf

## 📖 中文参考书籍

30. **万俊 (2024)**  
    *大语言模型应用指南：以ChatGPT为起点，从入门到精通的AI实践教程*  
    电子工业出版社，北京. ISBN: 978-7-121-47598-6

---

## 📝 使用说明

1. **学术研究**: 论文部分提供了智能体技术的理论基础和最新研究进展
2. **技术实践**: 在线资源和技术博客提供了实际应用指导和最佳实践
3. **行业趋势**: 行业观点文章帮助了解智能体技术的发展方向
4. **中文资源**: 中文参考书籍为国内读者提供了本土化的学习材料

## ⚠️ 访问提示

- 部分链接可能需要学术访问权限或订阅
- arXiv论文可免费访问
- 建议使用学术搜索引擎获取完整论文
- 在线资源链接如有失效，可通过搜索引擎查找最新地址

---

*本文档最后更新时间: 2025年1月*  
*如发现链接失效或有新的重要资源，欢迎在读者群中反馈*
