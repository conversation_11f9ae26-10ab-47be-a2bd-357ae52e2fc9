\ProvidesExplFile{\ExplFileName}{\ExplFileDate}{}{\ExplFileDescription}
\sys_if_engine_pdftex:TF
  {
    \ctex_zhmap_case:nnn
      {
        \setCJKmainfont
          [ BoldFont = FZHTK.TTF , ItalicFont = FZKTK.TTF ] { FZSSK.TTF }
        \setCJKsansfont [ BoldFont = FZHTK.TTF ] { FZXH1K.TTF }
        \setCJKmonofont { FZKTK.TTF }
        \setCJKfamilyfont { FZSSK } { FZSSK.TTF }
        \setCJKfamilyfont { FZHTK } { FZHTK.TTF }
        \setCJKfamilyfont { FZKTK } { FZKTK.TTF }
        \setCJKfamilyfont { FZFSK } { FZFSK.TTF }
        \ctex_punct_set:n { ml }
        \ctex_punct_map_family:nn { \CJKrmdefault } { FZSSK }
        \ctex_punct_map_family:nn { \CJKsfdefault } { zhheil }
        \ctex_punct_map_family:nn { \CJKttdefault } { FZFSK }
        \ctex_punct_map_itshape:nn { \CJKrmdefault } { FZKTK }
        \ctex_punct_map_bfseries:nn { \CJKrmdefault , FZSSK } { FZHTK }
        \ctex_punct_map_bfseries:nn { \CJKsfdefault } { FZHTK }
      }
      {
        \ctex_load_zhmap:nnnn { rm } { FZHTK } { FZKTK } { ml }
        \ctex_punct_set:n { ml }
        \ctex_punct_map_family:nn { \CJKrmdefault } { FZSSK }
        \ctex_punct_map_bfseries:nn { \CJKrmdefault } { FZHTK }
        \ctex_punct_map_itshape:nn { \CJKrmdefault } { FZKTK }
      }
      { \ctex_fontset_error:n { ml } }
  }
  {
    \setCJKmainfont
      [ BoldFont = FZHei-B01 , ItalicFont = FZKai-Z03 ] { FZShuSong-Z01 }
    \setCJKsansfont [ BoldFont = FZHei-B01 ] { FZXiHeiI-Z08 }
    \setCJKmonofont { FZKai-Z03 }
    \setCJKfamilyfont { FZSSK } [ BoldFont = FZXiaoBiaoSong-B05 ] { FZShuSong-Z01 }
    \setCJKfamilyfont { FZHTK } { FZHei-B01 }
    \setCJKfamilyfont { FZKTK } { FZKai-Z03 }
  }

\NewDocumentCommand \FZHTK { } { \CJKfamily { FZHTK } }
\NewDocumentCommand \FZKTK { } { \CJKfamily { FZKTK } }
