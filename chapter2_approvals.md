# 第2章批准内容

1. 第35页: 其搭载的各类传感器，接收来自环境的多种模态信息（例
2. 第36页: 发射激光束并测量反射信号的时间或相位差来精确测距，生成环境的三
3. 第37页: 屏幕截图、读取屏幕缓冲
4. 第38页: 融合来自摄像
5. 第38页: 三维重建（ 3D
6. 第38页: 模态间的互补和协同，实现对环境
7. 第39页: 视觉、听觉、触觉、嗅觉、味觉等多种感官通道与环境互动并体验世界。这些感知
8. 第39页: 深入理解各种单模态技术的原理和实现，我们可以为
9. 第39页: 视觉（阅读）或听觉（聆听）间接获取文本信息，而智能体则能直接处理数字化的文本序列，
10. 第39页: 词频和 TF-IDF等统计特征进行文本分类和情感分析，但该方法无法捕捉词汇间的
11. 第39页: 自注意力机制 高效地捕获上下文相关的
12. 第39页: 大量无标签文本的预训练，获得通用语言表示，并在具体任务上
13. 第40页: 在海量文本数据上的预训练，显著增强了文本理解、生成、推理和少样本学习能
14. 第40页: 引
15. 第40页: 引入
16. 第40页: 全局上下文推理和端到端集合预测方式，避免
17. 第41页: 无标签数据自
18. 第41页: 时间聚合或序列模型
19. 第41页: 随机掩码与重建机制，学习鲁棒的视频
20. 第41页: 声波形式传播，承载丰富的声学信息，包括语音内容、说话人身份、情感和环境声
21. 第42页: 非自回归并行预测音素时长、音高和能量，实现高质
22. 第42页: 电子鼻实现气味的检测与识别，应用于环境监测和医疗诊断。
23. 第42页: 电化学传感器模拟人类味觉，能够识别不同味道。
24. 第42页: 大型语言模型协调，将不
25. 第43页: 学习不同感知模态之间的
26. 第43页: 引入循环一致性损失增强了视觉与文
27. 第45页: 在包含屏幕截图和对应指
28. 第45页: 创新的架构和训练策略，大幅提升了对视觉（特别是屏幕视觉）和文本的联合理解能力，更精准
29. 第46页: "视觉想象"预判界面变化辅助决策） 、 PerAct、
30. 第46页: 综合视觉、语言和规划能力实现复杂的 GUI自动
31. 第46页: 精细的语音理解与文本映射，支持智能体处理复杂语音指令，例如用户语音请求"回
32. 第46页: 语音提出关于屏幕内容的问题
33. 第47页: 改进模型本
34. 第47页: 领域自适应微调 ，即在包含特定应用场景（如特定操作系统或软件界面截图、交
35. 第48页: 设计合理的输入提示（如系统角色定义、视觉标
36. 第48页: 检索增强生成（ RAG）技术，引入应用程序帮助文档、历史交互记录及 UI组件
37. 第48页: 领域自适应微调、参数高效
38. 第48页: 系统性的方法进一步提升
39. 第48页: 更优化的系统设计同样能够显著提升整体感知性能。
40. 第48页: 任务分解与信息共享（如界面监
41. 第48页: 分层智能体结构优化任务管理和执行。
42. 第49页: 预期 -重新评估机制、多智能体协作和专业
43. 第49页: 训练专门的 优化损失智能体 ，评估智能体的操作序列有效性、准确性与用户
44. 第49页: 演
45. 第49页: 实时在线纠错与反馈，及时修正智能体行为
46. 第50页: 精确理
47. 第50页: 多智能体协作理解视频编辑软件界面，辅助
48. 第50页: 详细视觉分析和指令理解，显著提升导航、信息查找和设置调整任务的表现；而 PC端
49. 第50页: 增强视觉解析模块和任务执行流程优化， 有效提升在 Windows 、macOS
50. 第50页: 语音快速
51. 第50页: 视觉理解确保精确操作反馈，并通过情感韵律进一步增强交
52. 第50页: 前面三个部分的系统性学习，我们已经全面了解了智能体感知系统的完整技术体系。从
53. 第51页: 因果推理区分屏幕反馈与用户指令之间的相关性与因果性，提升模型理解和交互能力。
54. 第51页: 以上方向的持续研究与创新，有望实现更智能、更可靠、适应复杂交互需求的智能体感