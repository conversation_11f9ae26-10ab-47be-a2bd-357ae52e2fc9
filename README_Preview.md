# 智能体系统架构实践 - 预览版本说明

## 概述

本项目提供了两个版本的PDF构建方式：
- **正式版本**: 使用 `build_pdf.sh` 构建，生成 `Main.pdf`
- **预览版本**: 使用 `build_preview_pdf.sh` 构建，生成 `Main_Preview.pdf`

## 预览版本特性

预览版本包含以下特殊功能：

### 水印设置
- **水印内容**: "预览版 | 18810901685"
- **水印样式**:
  - 半透明灰色文字 (gray!25)
  - 45度斜向放置
  - 优化字体大小 (0.9倍缩放)
  - 覆盖整个页面
  - 不影响正文阅读
  - 突出显示联系方式

### 使用场景
- 内部审阅和反馈收集
- 早期版本分享
- 草稿版本分发
- 保护正式版本内容

## 构建方法

### 构建预览版本
```bash
# 给脚本执行权限（首次使用）
chmod +x build_preview_pdf.sh

# 构建预览版PDF
./build_preview_pdf.sh
```

### 构建正式版本
```bash
# 给脚本执行权限（首次使用）
chmod +x build_pdf.sh

# 构建正式版PDF
./build_pdf.sh
```

## 文件结构

```
agent_book/
├── Main.tex                    # 正式版本主文件
├── Main_Preview.tex           # 预览版本主文件
├── build_pdf.sh              # 正式版本构建脚本
├── build_preview_pdf.sh       # 预览版本构建脚本
├── package.tex                # 共享包配置（不含水印）
├── chapters/                  # 章节文件
├── reference.bib             # 参考文献
└── README_Preview.md         # 本说明文件
```

## 技术实现

### 水印技术
使用LaTeX的 `draftwatermark` 包实现：
```latex
\usepackage{draftwatermark}
\SetWatermarkText{预览版 | 18810901685}
\SetWatermarkScale{0.9}
\SetWatermarkAngle{45}
\SetWatermarkColor{gray!25}
\SetWatermarkLightness{0.8}
```

### 优化特点

1. **极简设计**: 水印文本精简至"预览版 | 18810901685"
2. **联系突出**: 电话号码完整显示，便于直接联系
3. **视觉优化**: 使用竖线分隔符，更加简洁美观
4. **字体调整**: 0.9倍缩放确保文本清晰可读
5. **颜色优化**: gray!25提供更好的视觉效果

### 版本隔离
- 预览版本使用独立的 `Main_Preview.tex` 文件
- 正式版本的 `package.tex` 不包含水印包
- 两个版本共享相同的章节内容和样式

## 注意事项

1. **编译环境**: 需要完整的XeLaTeX环境和中文字体支持
2. **依赖包**: 预览版本需要额外的 `draftwatermark` 包
3. **文件大小**: 预览版本可能略大于正式版本
4. **兼容性**: 水印不影响PDF的打印和阅读功能

## 输出文件

- **正式版本**: `Main.pdf` (无水印，用于最终发布)
- **预览版本**: `Main_Preview.pdf` (带水印，用于内部审阅)

## 构建日志

构建过程会显示详细的编译信息，包括：
- 编译阶段进度
- 参考文献处理
- 最终文件大小
- 成功/失败状态

## 故障排除

如果构建失败，请检查：
1. XeLaTeX是否正确安装
2. 所需的LaTeX包是否完整
3. 中文字体是否可用
4. 文件权限是否正确

## 版本历史

- v1.0: 初始版本，支持基本水印功能
- v1.1: 优化水印样式和构建脚本
- v1.2: 添加自动清理和错误处理

---

**注意**: 预览版本仅供内部使用，请勿用于正式发布或外部分发。
