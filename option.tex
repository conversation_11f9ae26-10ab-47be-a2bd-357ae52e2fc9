%
% .:: footmisc ::.
%
% Circled Digit，带圆圈数字
\DefineFNsymbols{cd}{{\ding{172}}{\ding{173}}{\ding{174}}{\ding{175}}{\ding{176}}{\ding{177}}{\ding{178}}{\ding{179}}{\ding{180}}{\ding{181}}}
% Negative Circled Digit，反白带圆圈数字
\DefineFNsymbols{ncd}{{\ding{182}}{\ding{183}}{\ding{184}}{\ding{185}}{\ding{186}}{\ding{187}}{\ding{188}}{\ding{189}}{\ding{190}}{\ding{191}}}
% Circled Sans-Serif Digit，带圆圈无衬线数字
\DefineFNsymbols{cssd}{{\ding{192}}{\ding{193}}{\ding{194}}{\ding{195}}{\ding{196}}{\ding{197}}{\ding{198}}{\ding{199}}{\ding{200}}{\ding{201}}}
% Negative Circled Sans-Serif Digit，反白带圆圈无衬线数字
\DefineFNsymbols{ncssd}{{\ding{202}}{\ding{203}}{\ding{204}}{\ding{205}}{\ding{206}}{\ding{207}}{\ding{208}}{\ding{209}}{\ding{210}}{\ding{211}}}
\setfnsymbol{cssd}
\setlength\footnotemargin{.6em}

% package: float
\floatplacement{figure}{!ht}
\floatplacement{table}{!h}

\setlength\fboxrule{.25pt}
\setlength\fboxsep{0pt}


% package: booktab
\setlength\aboverulesep{0sp}
\setlength\belowrulesep{0sp}
\setlength\cmidrulesep{0sp}

% package: graphicx
\graphicspath{{resources/}}

% package: caption
\DeclareCaptionFont{thin}{\FZXH1K\MyriadProLight}
\captionsetup{format=hang,font=thin,labelsep=quad}
% \DeclareCaptionFont{31104}{\FZFSK}
% \captionsetup{format=hang,labelsep=quad,font=31104}
% \DeclareCaptionFormat{31104}{%
%   \faFileCodeO\enspace\bfseries#3
% }
% \captionsetup[lstlisting]{format=31104,singlelinecheck=off}

% package: enumitem
\setlist{partopsep=0pt,topsep=0pt,parsep=1em}
\setlist[1]{itemsep=1em plus 1pt minus 1pt}

% \setlist{noitemsep,partopsep=0pt}%,listparindent=\parindent
% \setlist[1]{labelindent=\parindent}
% \setlist[itemize]{topsep=0pt,parsep=.5\parskip,leftmargin=3.5em}
\setlist[itemize]{parsep=.0\parskip,leftmargin=1.5em,labelindent=2em}
\setlist[itemize,1]{parsep=.0\parskip,leftmargin=3.5em}
\setlist[enumerate]{parsep=.0\parskip,leftmargin=0em,itemindent=3.75em,labelindent=2em,listparindent=2em}
\setlist[enumerate,1]{label=(\arabic*)}
% \setlist[description]{topsep=0pt,parsep=0\parskip,labelsep=.25em,leftmargin=*}

% 图表编号分隔符
\makeatletter
% \renewcommand\thefigure{\ifnum \c@chapter>\z@ \thechapter -\fi \@arabic\c@figure}
% \renewcommand\thetable{\ifnum \c@chapter>\z@ \thechapter -\fi \@arabic\c@table}
\makeatother

%*********************
%* 设置图片默认宽度  *
%*********************
\setkeys{Gin}{width=.9\textwidth,height=.9\textheight,keepaspectratio}

% CTEX

% 设置章节编号深度，确保subsubsection也显示编号
\setcounter{secnumdepth}{3}

\providecommand\backgroundsetup{
  \begin{tikzpicture}[remember picture, overlay]
 %   \draw (0,-.1) -- (5.2,-.1);
 %   \draw (2.1,1) -- (2.1,-2.5);
 %   \draw (3.4,2) -- (3.4,-.84);
 %   \filldraw[fill=lightgray] (1.7,.57) circle(1.21);
  \end{tikzpicture}
}

\newcommand\chapternumformat[1]{\makebox[1in][c]{#1}}

\ctexset{
  fontset = ml,
  chapter = {
%    name = {},
    number = \arabic{chapter},
    format = \CTEXifname{\backgroundsetup}{\vspace*{-60pt}},
    nameformat = \bfseries\LARGE,
    numberformat = \sffamily\bfseries\itshape\fontsize{42}{42}\selectfont\makebox[.75in][c],
    aftername = \par\bigskip,
    titleformat = \raggedright\FZDBSK\fontsize{24}{24}\selectfont,%\CTEXifname{\hspace{85pt}}{}
    beforeskip = \CTEXifname{72pt}{24pt},
    afterskip = \CTEXifname{50pt}{36pt},
    pagestyle = empty
  },
  section = {
    format = \sffamily\FZHTK\fontsize{14}{14}\selectfont,
    beforeskip = 0pt,
    afterskip = 0pt
  },
  subsection = {
    format = \FZDBSK\fontsize{11}{11}\selectfont,
    beforeskip = 0pt,
    afterskip = 0pt
  },
  subsubsection = {
    format = \FZDBSK\fontsize{10}{12}\selectfont,
    beforeskip = 0pt,
    afterskip = 0pt,
    number = \thesubsubsection
  }
}

\makeatletter
\g@addto@macro\normalsize{\setlength\abovedisplayskip{7pt}}
\g@addto@macro\normalsize{\setlength\belowdisplayskip{7pt}}
\g@addto@macro\normalsize{\setlength\abovedisplayshortskip{7pt}}
\g@addto@macro\normalsize{\setlength\belowdisplayshortskip{7pt}}
\AtBeginEnvironment{figure}{\def\@floatboxreset{\setlength{\abovedisplayskip}{0pt}}}

\makeatother 