\usepackage[fontset=none,10pt,heading]{ctex}
\usepackage{fontspec}
\usepackage{pifont}
\usepackage{fontawesome}

% 设置常用字体：跨平台兼容
\IfFontExistsTF{Times New Roman}{
  \setmainfont{Times New Roman}
}{
  \setmainfont{Times}
}

% 中文字体设置 - 优先使用Mac系统字体，备选Windows字体
\IfFontExistsTF{STSong}{
  \setCJKmainfont[BoldFont=STHeiti,ItalicFont=STKaiti]{STSong}
  \setCJKsansfont{STHeiti}
  \setCJKmonofont{STFangsong}
}{
  \IfFontExistsTF{SimSun}{
    \setCJKmainfont{SimSun}
    \setCJKsansfont{SimHei}
    \setCJKmonofont{FangSong}
  }{
    % 使用系统默认中文字体
    \setCJKmainfont{Songti SC}
    \setCJKsansfont{Heiti SC}
    \setCJKmonofont{STFangsong}
  }
}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{bm}
\usepackage{array}
\usepackage{enumitem}
\usepackage{imakeidx}
\usepackage[numbers,sort&compress]{natbib}
\usepackage{geometry}
\usepackage{float}
\usepackage{setspace}
\usepackage[colorlinks,linkcolor=black,anchorcolor=black,citecolor=black]{hyperref}
\usepackage{bookmark}
\usepackage{caption}
\usepackage[perpage,symbol*,bottom,hang,stable,multiple]{footmisc}
% \usepackage[bottom,hang,stable,multiple]{footmisc}
\usepackage[clearempty,pagestyles]{titlesec}
\usepackage[cmyk,table,hyperref]{xcolor}
\usepackage{tikz}
\usetikzlibrary{shapes.geometric, arrows.meta, positioning, calc, backgrounds, fit}
\usepackage{tcolorbox}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{listings}
\usepackage{booktabs}
\usepackage{longtable}
\usepackage{makecell}
\usepackage{tabu}
\usepackage{mdframed}
\usepackage{wrapfig}
\usepackage{pdfpages}
\usepackage{qrcode}
\usepackage{chngcntr}
\usepackage{url}
\urlstyle{rm}
% \usepackage[toc]{multitoc}
\usepackage[nottoc,chapter]{tocbibind}
\usepackage{diagbox} % 斜线表头
% 第一步：在导言区添加包（如果还没有的话）
\usepackage{tikz}
\usetikzlibrary{shapes.geometric, arrows, positioning, fit}
\AtEndPreamble{
	\usepackage[nohyphen,strings]{underscore}
}
