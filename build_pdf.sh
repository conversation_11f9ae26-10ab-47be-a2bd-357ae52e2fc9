#!/bin/bash
# 自动编译LaTeX文档并清理辅助文件，仅保留Main.pdf
set -e

# 创建临时目录
mkdir -p tmp

# 删除 Main.pdf
rm -f Main.pdf


xelatex -output-directory=./ Main.tex
xelatex -output-directory=./ Main.tex
rm -f *.aux *.log *.toc *.out *.bbl *.bcf *.run.xml *.fdb_latexmk *.fls *.synctex.gz *.blg

# 第一次编译，生成aux文件
# xelatex -output-directory=tmp Main.tex

# # 拷贝参考文献相关文件到临时目录
# cp reference.bib gbt7714-numerical.bst tmp/

# # 处理参考文献
# (cd tmp && bibtex Main)

# # 再编译两次，确保引用和目录正确
# xelatex -output-directory=tmp Main.tex
# xelatex -output-directory=tmp Main.tex

# # 拷贝PDF到主目录
# cp tmp/Main.pdf .

# # 清理临时目录
# rm -r tmp

# # 清理主目录下常见的LaTeX中间文件
# rm -f *.aux *.log *.toc *.out *.bbl *.bcf *.run.xml *.fdb_latexmk *.fls *.synctex.gz *.blg

echo "编译完成，已生成 Main.pdf。"