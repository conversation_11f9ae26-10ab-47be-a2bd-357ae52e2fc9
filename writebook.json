{"name": "Book Writer Agent", "nodes": [{"parameters": {}, "name": "Webhook Input", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 200], "webhookId": "book-writer-start"}, {"parameters": {"functionCode": "const input = $json.body; return [{ json: { topic: input.topic || '人工智能与人类未来', audience: input.audience || '普通大众', chapters: parseInt(input.chapters || 5) } }];"}, "name": "Set Book Variables", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [400, 200]}, {"parameters": {"model": "gpt-4", "prompt": "根据主题 {{$json.topic}}，为{{$json.audience}} 生成 {{$json.chapters}} 个章节标题，格式为 JSON 数组。"}, "name": "Generate Chapters", "type": "n8n-nodes-base.openai", "typeVersion": 1, "position": [600, 200]}, {"parameters": {"model": "gpt-4", "prompt": "为章节『{{$json.chapter}}』生成 3 个小节标题，格式为 JSON。"}, "name": "Generate Subsections", "type": "n8n-nodes-base.openai", "typeVersion": 1, "position": [800, 200]}, {"parameters": {"model": "gpt-4", "prompt": "根据小节标题『{{$json.subsection}}』撰写约 300 字内容，适合 {{$json.audience}} 阅读。"}, "name": "Generate Content", "type": "n8n-nodes-base.openai", "typeVersion": 1, "position": [1000, 200]}, {"parameters": {"model": "gpt-4", "prompt": "对下面内容评分（语言/逻辑/流畅度 1-10 分）：{{$json.text}}"}, "name": "Rate Section", "type": "n8n-nodes-base.openai", "typeVersion": 1, "position": [1200, 200]}, {"parameters": {"functionCode": "return [{ json: { markdown: `## ${$json.chapter}\\n### ${$json.subsection}\\n${$json.text}\\n---` } }];"}, "name": "Format Markdown", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1400, 200]}, {"parameters": {"functionCode": "let toc = '# 目录\\n'; $items().forEach((item, index) => { toc += `- [${item.json.chapter}](#${item.json.chapter.replace(/\\s+/g, '-')})\\n`; }); return [{ json: { toc } }];"}, "name": "Generate TOC", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1600, 200]}, {"parameters": {"functionCode": "const content = $json.toc + '\\n\\n' + $json.markdown; return [{ json: { finalMarkdown: content } }];"}, "name": "Merge Content", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1800, 200]}, {"parameters": {"functionCode": "const words = $json.finalMarkdown.split(/\\\\s+/).length; const minutes = Math.ceil(words / 300); return [{ json: { finalMarkdown: `${$json.finalMarkdown}\\n\\n阅读时长：约 ${minutes} 分钟，共 ${words} 字` } }];"}, "name": "Add Stats", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"url": "https://api.html2pdf.app/v1/generate", "method": "POST", "jsonParameters": true, "bodyParametersJson": "{ \"html\": \"<pre>{{ $json.finalMarkdown }}<pre>\", \"apiKey\": \"your-html2pdf-key\" }"}, "name": "Convert to PDF", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2200, 200]}, {"parameters": {"authentication": "oAuth2", "binaryData": true, "fileName": "Book.pdf", "folderId": "your-folder-id"}, "name": "Upload to Google Drive", "type": "n8n-nodes-base.googleDrive", "typeVersion": 1, "position": [2400, 200]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "你的新书已生成", "text": "PDF 文件已上传 Google Drive。"}, "name": "Send Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [2600, 200]}], "connections": {"Webhook Input": {"main": [[{"node": "Set Book Variables", "type": "main", "index": 0}]]}, "Set Book Variables": {"main": [[{"node": "Generate Chapters", "type": "main", "index": 0}]]}, "Generate Chapters": {"main": [[{"node": "Generate Subsections", "type": "main", "index": 0}]]}, "Generate Subsections": {"main": [[{"node": "Generate Content", "type": "main", "index": 0}]]}, "Generate Content": {"main": [[{"node": "Rate Section", "type": "main", "index": 0}]]}, "Rate Section": {"main": [[{"node": "Format Markdown", "type": "main", "index": 0}]]}, "Format Markdown": {"main": [[{"node": "Generate TOC", "type": "main", "index": 0}]]}, "Generate TOC": {"main": [[{"node": "Merge Content", "type": "main", "index": 0}]]}, "Merge Content": {"main": [[{"node": "Add Stats", "type": "main", "index": 0}]]}, "Add Stats": {"main": [[{"node": "Convert to PDF", "type": "main", "index": 0}]]}, "Convert to PDF": {"main": [[{"node": "Upload to Google Drive", "type": "main", "index": 0}]]}, "Upload to Google Drive": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}}}