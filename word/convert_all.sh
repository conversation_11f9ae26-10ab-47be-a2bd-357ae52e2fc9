#!/bin/bash

# This script should be run from the project root directory.

# Create the output directory if it doesn't exist
mkdir -p word

echo "Converting individual chapters to Word documents..."

# Loop through all .tex files in the chapters directory
for file in chapters/*.tex; do
  # Get the filename without the directory and extension
  filename=$(basename -- "$file" .tex)
  echo "Converting $filename.tex..."
  # Convert the file to docx and save it in the word directory
  pandoc "$file" --bibliography=reference.bib -o "word/$filename.docx"
done

echo "Individual chapter conversions completed."
echo ""
echo "Creating combined Word document with intro + all chapters in correct order (intro + 1-12)..."

# Create a temporary file to combine all chapters
temp_file="word/temp_combined.tex"

# Start with document header
cat > "$temp_file" << 'EOF'
\documentclass{book}
\usepackage[utf8]{inputenc}
\usepackage{ctex}
\usepackage{fontspec}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{hyperref}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{booktabs}
\usepackage{longtable}
\usepackage{array}
\usepackage{multirow}
\usepackage{wrapfig}
\usepackage{float}
\usepackage{colortbl}
\usepackage{pdflscape}
\usepackage{tabu}
\usepackage{threeparttable}
\usepackage{threeparttablex}
\usepackage{makecell}
\usepackage{xcolor}

% 设置常用字体：跨平台兼容
\IfFontExistsTF{Times New Roman}{
  \setmainfont{Times New Roman}
}{
  \setmainfont{Times}
}

% 中文字体设置 - 优先使用Mac系统字体，备选Windows字体
\IfFontExistsTF{STSong}{
  \setCJKmainfont[BoldFont=STHeiti,ItalicFont=STKaiti]{STSong}
  \setCJKsansfont{STHeiti}
  \setCJKmonofont{STFangsong}
}{
  \IfFontExistsTF{SimSun}{
    \setCJKmainfont{SimSun}
    \setCJKsansfont{SimHei}
    \setCJKmonofont{FangSong}
  }{
    % 使用系统默认中文字体
    \setCJKmainfont{Songti SC}
    \setCJKsansfont{Heiti SC}
    \setCJKmonofont{STFangsong}
  }
}

\title{自主进化智能体}
\author{}
\date{}

\begin{document}
\frontmatter
\maketitle
\tableofcontents
\mainmatter

EOF

# Define the correct order: intro + chapters (1-12)
chapters_order=(
  "chapters/intro.tex"
  "chapters/chapter1.tex"
  "chapters/chapter2.tex"
  "chapters/chapter3.tex"
  "chapters/chapter4.tex"
  "chapters/chapter5.tex"
  "chapters/chapter6.tex"
  "chapters/chapter7.tex"
  "chapters/chapter8.tex"
  "chapters/chapter9.tex"
  "chapters/chapter10.tex"
  "chapters/chapter11.tex"
  "chapters/chapter12.tex"
)

# Add intro and each chapter content in the correct order
for file in "${chapters_order[@]}"; do
  if [ -f "$file" ]; then
    filename=$(basename -- "$file" .tex)
    echo "Adding $filename to combined document..."

    # 确定章节号
    if [[ "$filename" == "intro" ]]; then
      chapter_num=""
    else
      chapter_num=$(echo "$filename" | sed 's/chapter//')
    fi

    # 创建临时文件处理当前章节
    temp_chapter="word/temp_${filename}.tex"

    # Extract content and clean document structure
    sed -e '/^\\documentclass/d' \
        -e '/^\\usepackage/d' \
        -e '/^\\begin{document}/d' \
        -e '/^\\end{document}/d' \
        -e '/^\\input{package}/d' \
        -e '/^\\input{ctex}/d' \
        "$file" > "$temp_chapter"

    # 处理图表编号（只对非intro章节）
    if [[ "$filename" != "intro" ]]; then
      # 使用更简单高效的方法：一次性处理所有caption

      # 创建一个awk脚本来处理图表编号
      awk -v chapter="$chapter_num" '
      BEGIN {
        fig_count = 1
        tab_count = 1
        alg_count = 1
        in_figure = 0
        in_table = 0
        in_algorithm = 0
      }
      /\\begin\{figure\}/ { in_figure = 1 }
      /\\begin\{table\}/ { in_table = 1 }
      /\\begin\{algorithm\}/ { in_algorithm = 1 }
      /\\caption\{/ {
        if (in_figure) {
          gsub(/\\caption\{/, "\\caption{图" chapter "." fig_count " ")
          fig_count++
        } else if (in_table) {
          gsub(/\\caption\{/, "\\caption{表" chapter "." tab_count " ")
          tab_count++
        } else if (in_algorithm) {
          gsub(/\\caption\{/, "\\caption{算法" chapter "." alg_count " ")
          alg_count++
        }
      }
      /\\end\{figure\}/ { in_figure = 0 }
      /\\end\{table\}/ { in_table = 0 }
      /\\end\{algorithm\}/ { in_algorithm = 0 }
      { print }
      ' "$temp_chapter" > "word/temp_${filename}_numbered.tex"

      # 使用编号后的文件
      mv "word/temp_${filename}_numbered.tex" "$temp_chapter"
    fi

    # 添加处理后的内容到主文件
    cat "$temp_chapter" >> "$temp_file"

    # 清理临时文件
    rm "$temp_chapter"

    # Add page break after each chapter (except the last one)
    echo "" >> "$temp_file"
    echo "\\newpage" >> "$temp_file"
    echo "" >> "$temp_file"
  else
    echo "Warning: $file not found, skipping..."
  fi
done

# Close the document
echo '\end{document}' >> "$temp_file"

# Convert the combined file to Word
echo "Converting combined document to Word format..."

# 检查是否存在参考文档，如果不存在则创建一个简单的
if [ ! -f "word/reference.docx" ]; then
  echo "Creating reference document for consistent formatting..."
  # 创建一个简单的参考文档
  echo '\documentclass{book}
\usepackage{ctex}
\begin{document}
\chapter{示例章节}
这是一个示例段落。
\section{示例小节}
这是另一个示例段落。
\subsection{示例子小节}
这是第三级示例段落。
\begin{figure}[h]
\centering
\caption{示例图片}
\end{figure}
\begin{table}[h]
\centering
\caption{示例表格}
\begin{tabular}{|c|c|}
\hline
列1 & 列2 \\
\hline
数据1 & 数据2 \\
\hline
\end{tabular}
\end{table}
\end{document}' > word/temp_ref.tex

  pandoc word/temp_ref.tex --number-sections -o word/reference.docx
  rm word/temp_ref.tex
fi

pandoc "$temp_file" \
  --bibliography=reference.bib \
  --number-sections \
  --toc \
  --toc-depth=3 \
  --reference-doc=word/reference.docx \
  --resource-path=.:imgs:imgs/ch1:imgs/ch2:imgs/ch3:imgs/ch4:imgs/ch5:imgs/ch6:imgs/ch7:imgs/ch8:imgs/ch9:imgs/ch10:imgs/ch11:imgs/ch12 \
  --extract-media=word/media \
  -o "word/自进化智能体动态记忆与持续运行的架构实践.docx"

# Clean up temporary file
rm "$temp_file"

echo ""
echo "✅ All conversions completed!"
echo "📁 Individual chapters: word/chapter*.docx"
echo "📖 Complete book (Intro + Chapters 1-12): word/自进化智能体动态记忆与持续运行的架构实践.docx"
