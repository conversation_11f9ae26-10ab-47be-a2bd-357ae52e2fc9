# 全书参考文献

## 学术期刊文章

1. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2003). A neural probabilistic language model. *Journal of Machine Learning Research*, 3, 1137-1155.

2. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & others. (2021). Program synthesis with large language models. *arXiv preprint arXiv:2108.07732*.

3. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, H. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & others. (2021). Evaluating large language models trained on code. *arXiv preprint arXiv:2107.03374*.

4. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & others. (2023). Agentbench: Evaluating llms as agents. *arXiv preprint arXiv:2308.03688*.

5. <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON>. (2025). Autoeval: A practical framework for autonomous evaluation of mobile agents. *arXiv preprint arXiv:2503.02403*.

6. Valmeekam, K., Marquez, M., Olmo, A., Sreedharan, S., & Kambhampati, S. (2023). Planbench: An extensible benchmark for evaluating large language models on planning and reasoning about change. *Advances in Neural Information Processing Systems*, 36, 38975-38987.

7. Li, M., Zhao, Y., Yu, B., Song, F., Li, H., Yu, H., Li, Z., Huang, F., & Li, Y. (2023). Api-bank: A comprehensive benchmark for tool-augmented llms. *arXiv preprint arXiv:2304.08244*.

8. Wang, X., Wang, Z., Liu, J., Chen, Y., Yuan, L., Peng, H., & Ji, H. (2023). Mint: Evaluating llms in multi-turn interaction with tools and language feedback. *arXiv preprint arXiv:2309.10691*.

9. Wang, Z., Zhou, S., & Fried, D. (2022). Execution-based evaluation for open-domain code generation. *arXiv preprint arXiv:2212.10481*.

10. Lai, Y., Li, C., Wang, Y., Zhang, T., Zhong, R., Zettlemoyer, L., Yih, W.-t., Fried, D., Wang, S., & Yu, T. (2023). DS-1000: A natural and reliable benchmark for data science code generation. In *International Conference on Machine Learning* (pp. 18319-18345). PMLR.

11. He, K., Zhang, X., Ren, S., & Sun, J. (2016). Deep Residual Learning for Image Recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition* (pp. 770-778). IEEE.

12. Wei, J., Wang, X., Schuurmans, D., Bosma, M., Xia, F., Chi, E., Le, Q. V., Zhou, D., & others. (2022). Chain-of-thought prompting elicits reasoning in large language models. *Advances in neural information processing systems*, 35, 24824-24837.

13. Yao, S., Yu, D., Zhao, J., Shafran, I., Griffiths, T., Cao, Y., & Narasimhan, K. (2023). Tree of thoughts: Deliberate problem solving with large language models. *Advances in neural information processing systems*, 36, 11809-11822.

14. Morris, M. R., Sohl-Dickstein, J., Fiedel, N., Warkentin, T., Dafoe, A., Faust, A., Farabet, C., & Legg, S. (2024). Position: Levels of AGI for operationalizing progress on the path to AGI. In *Forty-first International Conference on Machine Learning*.

15. Wooldridge, M., & Jennings, N. R. (1995). Agent theories, architectures, and languages: A survey. *Intelligent agents*, 1-39. Springer.

## 会议论文

16. Mikolov, T., Sutskever, I., Chen, K., Corrado, G. S., & Dean, J. (2013). Distributed representations of words and phrases and their compositionality. In C. Burges, L. Bottou, & M. Welling (Eds.), *Advances in neural information processing systems* (pp. 3111-3119). Curran Associates Inc.

17. Yao, S., Zhao, J., Yu, D., Du, N., Shafran, I., Narasimhan, K., & Cao, Y. (2023). React: Synergizing reasoning and acting in language models. In *International Conference on Learning Representations (ICLR)*.

## 在线资源

18. Gates, B. (2023, March 22). The Age of AI Has Begun. *Gates Notes*. https://www.gatesnotes.com/the-age-of-ai-has-begun

19. Karpathy, A. (2025). Software is Changing. *YouTube*. https://www.youtube.com/watch?v=LCEmiRjPEtQ

20. OpenAI. (2025, February 2). Introducing Deep Research. *OpenAI Blog*. https://openai.com/index/introducing-deep-research/

21. OpenAI. (2025, January 23). Introducing Operator. *OpenAI Blog*. https://openai.com/index/introducing-operator/

22. Anthropic. (2024, November 25). Introducing the Model Context Protocol. *Anthropic News*. https://www.anthropic.com/news/model-context-protocol

23. Bloomberg. (2024, July 12). OpenAI Scale Ranks Progress Toward 'Human-Level' Problem Solving. *Bloomberg News*. https://www.bloomberg.com/news/articles/2024-07-11/openai-sets-levels-to-track-progress-toward-superintelligent-ai

24. Schluntz, E., & Zhang, B. (2024, December 19). Building effective agents. *Anthropic Engineering Blog*. https://www.anthropic.com/engineering/building-effective-agents

25. OpenAI. (2024). A Practical Guide to Building Agents. *OpenAI Business Guide*. https://cdn.openai.com/business-guides-and-resources/a-practical-guide-to-building-agents.pdf

26. PPC. (2024, January). Agents2: New White Paper on Agent Technology. *PPC White Paper*. https://ppc.land/content/files/2025/01/Newwhitepaper_Agents2.pdf

## 预印本论文

27. Liu, B., Li, X., Zhang, J., Wang, J., He, T., Hong, S., Liu, H., Zhang, S., Song, K., Zhu, K., Cheng, Y., Wang, S., Luo, Y., Jin, H., Zhang, P., Liu, O., Chen, J., Zhang, H., Yu, Z., Shi, H., Li, B., Teng, F., Jia, X., Xu, J., Xiang, J., Lin, Y., Liu, T., Liu, T., Su, Y., Sun, H., Berseth, G., Nie, J., Foster, I., Ward, L., Wu, Q., Gu, Y., Zhuge, M., Tang, X., Wang, H., You, J., Wang, C., Pei, J., Yang, Q., Qi, X., Wu, C., et al. (2025). Advances and Challenges in Foundation Agents: From Brain-Inspired Intelligence to Evolutionary, Collaborative, and Safe Systems. *arXiv preprint arXiv:2504.01990*. https://arxiv.org/abs/2504.01990

## 图书

28. Russell, S. J., & Norvig, P. (2020). *Artificial Intelligence: A Modern Approach* (3rd ed.). Pearson.

29. Winston, P. H. (1992). *Artificial intelligence*. Addison-Wesley Longman Publishing Co., Inc.

30. 万俊. (2024). *大语言模型应用指南：以ChatGPT为起点，从入门到精通的AI实践教程*. 电子工业出版社.