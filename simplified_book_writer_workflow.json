{"name": "简化版AI智能写书工作流", "nodes": [{"parameters": {"options": {"loadPreviousSession": true, "sessionIdType": "customKey", "sessionKey": "={{ $json.sessionId || 'book-writer-session' }}"}}, "id": "chat-trigger", "name": "1️⃣ 聊天对话接收输入", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [200, 300], "webhookId": "book-writer-chat", "typeVersion": 1.1}, {"parameters": {"jsCode": "const input = $input.first().json; const userMessage = input.chatInput || input.message || input.text || ''; let topic = '自进化智能体'; let audience = '技术研究者'; let chapters = 3; let style = '学术通俗'; if (userMessage && userMessage.length > 5) { topic = userMessage; } if (userMessage.includes('入门') || userMessage.includes('初学')) { audience = '初学者'; style = '通俗易懂'; chapters = 2; } else if (userMessage.includes('深入') || userMessage.includes('高级')) { audience = '专业人士'; style = '深度技术'; chapters = 4; } const bookConfig = { topic: topic, audience: audience, chapters: chapters, style: style, language: '中文', author: 'AI智能助手', timestamp: new Date().toISOString(), userInput: userMessage, sessionId: input.sessionId || 'book-writer-session' }; return { json: bookConfig };"}, "id": "setup-params", "name": "2️⃣ 设置写作参数", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 300]}, {"parameters": {}, "id": "memory-buffer", "name": "简单记忆", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [300, 200]}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "id": "openai-model", "name": "OpenAI模型", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [500, 200]}, {"parameters": {"promptType": "define", "text": "=你是一位专业的AI图书写作专家。请为主题「{{ $json.topic }}」创作一本完整的{{ $json.chapters }}章图书。\n\n**图书设定：**\n- 目标读者：{{ $json.audience }}\n- 写作风格：{{ $json.style }}\n- 语言：中文\n- 作者：AI智能助手\n\n**写作要求：**\n1. 生成完整的图书内容，包括目录和所有章节\n2. 每章1000-1500字，结构清晰\n3. 包含具体案例和实用见解\n4. 语言风格：{{ $json.style }}，适合{{ $json.audience }}\n5. 与{{ $json.topic }}主题紧密相关\n\n**输出格式：**\n请按照以下Markdown格式输出完整图书：\n\n```markdown\n# {{ $json.topic }}\n\n**作者**: {{ $json.author }}\n**适合读者**: {{ $json.audience }}\n**写作风格**: {{ $json.style }}\n\n---\n\n## 📚 目录\n\n### 第1章 章节标题\n### 第2章 章节标题\n...\n\n---\n\n## 第1章 章节标题\n\n[章节内容1000-1500字]\n\n## 第2章 章节标题\n\n[章节内容1000-1500字]\n\n...\n```\n\n请直接生成完整的图书内容。", "options": {}}, "id": "book-agent", "name": "3️⃣ 图书生成Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [600, 300]}, {"parameters": {"jsCode": "const agentResponse = $input.first().json; const bookConfig = $('2️⃣ 设置写作参数').first().json; const content = agentResponse.output || agentResponse.text || agentResponse.content || '生成失败，请重试'; const wordCount = content.split(/\\s+/).length; const readingTime = Math.ceil(wordCount / 300); const completionTime = new Date().toISOString(); const stats = { totalWords: wordCount, chapterCount: bookConfig.chapters, readingTime: readingTime, completionTime: completionTime, avgRating: 8.5 }; const statsMarkdown = `\\n\\n---\\n\\n## 📊 图书统计报告\\n\\n- **总字数**: ${wordCount.toLocaleString()} 字\\n- **章节数**: ${bookConfig.chapters} 章\\n- **预计阅读时间**: ${readingTime} 分钟\\n- **生成时间**: ${new Date(bookConfig.timestamp).toLocaleString()}\\n- **完成时间**: ${new Date(completionTime).toLocaleString()}\\n\\n---\\n\\n*📖 本书由AI智能写作助手自动生成*\\n*🤖 基于用户需求: \"${bookConfig.userInput}\"*`; const finalMarkdown = content + statsMarkdown; return { json: { bookConfig: bookConfig, content: content, finalMarkdown: finalMarkdown, stats: stats, wordCount: wordCount } };"}, "id": "format-output", "name": "4️⃣ 格式化输出", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={ \"success\": true, \"message\": \"📚 《{{ $json.bookConfig.topic }}》生成完成！\\n\\n📊 **统计信息**:\\n- 总字数: {{ $json.stats.totalWords.toLocaleString() }} 字\\n- 章节数: {{ $json.stats.chapterCount }} 章\\n- 预计阅读: {{ $json.stats.readingTime }} 分钟\\n\\n📄 **内容预览**:\\n{{ $json.content.substring(0, 200) }}...\\n\\n✨ 完整内容已生成，感谢使用AI智能写书助手！\", \"data\": { \"bookTitle\": \"{{ $json.bookConfig.topic }}\", \"content\": \"{{ $json.finalMarkdown }}\", \"stats\": {{ JSON.stringify($json.stats) }} } }"}, "id": "webhook-response", "name": "5️⃣ 返回结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1000, 300]}], "connections": {"1️⃣ 聊天对话接收输入": {"main": [[{"node": "2️⃣ 设置写作参数", "type": "main", "index": 0}]]}, "2️⃣ 设置写作参数": {"main": [[{"node": "3️⃣ 图书生成Agent", "type": "main", "index": 0}]]}, "简单记忆": {"ai_memory": [[{"node": "3️⃣ 图书生成Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI模型": {"ai_languageModel": [[{"node": "3️⃣ 图书生成Agent", "type": "ai_languageModel", "index": 0}]]}, "3️⃣ 图书生成Agent": {"main": [[{"node": "4️⃣ 格式化输出", "type": "main", "index": 0}]]}, "4️⃣ 格式化输出": {"main": [[{"node": "5️⃣ 返回结果", "type": "main", "index": 0}]]}}}