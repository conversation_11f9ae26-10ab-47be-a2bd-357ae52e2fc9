# agent_book

## 环境需求

- XeLaTeX（推荐使用TeX Live 2021及以上版本，或MacTeX）
- 推荐安装字体：
  - Times New Roman
  - Arial
  - FZKai-Z03
  - FZDaBiaoSong-B06S
  - FZXiHeiI-Z08

## 如何生成PDF

1. **一键编译（推荐）**

   已提供自动化脚本 `build_pdf.sh`，只需运行：

   ```bash
   ./build_pdf.sh
   ```
   
   该脚本会自动完成所有编译、参考文献处理和辅助文件清理，最终只保留 `Main.pdf`，并自动删除所有常见的LaTeX中间文件。

2. **手动编译方式（适合只用xelatex）**

   ```bash
   mkdir -p tmp
   xelatex -output-directory=tmp Main.tex
   bibtex tmp/Main
   xelatex -output-directory=tmp Main.tex
   xelatex -output-directory=tmp Main.tex
   cp tmp/Main.pdf .
   rm -r tmp
   ```

3. **注意事项**
   - 参考文献文件为`reference.bib`，会自动处理。
   - 所有辅助文件会被输出到`tmp/`目录，主目录只保留PDF。
   - 脚本会自动清理所有常见的LaTeX中间文件。
   - 若缺少字体或宏包，请根据报错信息安装。
