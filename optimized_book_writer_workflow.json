{
  "name": "自进化智能体写书工作流",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "chat-book-writer",
        "responseMode": "responseNode",
        "options": {
          "noResponseBody": false
        }
      },
      "id": "chat-input",
      "name": "1️⃣ 聊天对话接收输入",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [200, 300],
      "webhookId": "chat-book-writer"
    },
    {
      "parameters": {
        "jsCode": "// 解析聊天输入，设置默认值为自进化智能体主题\nconst input = $input.first().json.body || $input.first().json;\nconst userMessage = input.message || input.text || input.topic || '';\n\n// 智能解析用户意图\nlet topic = '自进化智能体';\nlet audience = '技术研究者';\nlet chapters = 8;\nlet style = '学术通俗';\n\n// 如果用户提供了具体主题，则使用用户的\nif (userMessage && userMessage.length > 5) {\n  topic = userMessage;\n}\n\n// 根据关键词调整参数\nif (userMessage.includes('入门') || userMessage.includes('初学')) {\n  audience = '初学者';\n  style = '通俗易懂';\n  chapters = 6;\n} else if (userMessage.includes('深入') || userMessage.includes('高级')) {\n  audience = '专业人士';\n  style = '深度技术';\n  chapters = 10;\n}\n\nconst bookConfig = {\n  topic: topic,\n  audience: audience,\n  chapters: chapters,\n  style: style,\n  language: '中文',\n  author: 'AI智能助手',\n  timestamp: new Date().toISOString(),\n  userInput: userMessage\n};\n\nreturn { json: bookConfig };"
      },
      "id": "setup-params",
      "name": "2️⃣ 设置写作参数",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [400, 300]
    },
    {
      "parameters": {
        "resource": "chat",
        "operation": "create",
        "chatId": "gpt-4",
        "text": "=作为AI图书策划专家，请为主题「{{ $json.topic }}」设计一本{{ $json.chapters }}章的专业图书大纲。\n\n图书信息：\n- 目标读者：{{ $json.audience }}\n- 写作风格：{{ $json.style }}\n- 用户原始需求：{{ $json.userInput }}\n\n请设计章节结构，每章包含3-4个小节。要求：\n1. 章节标题要专业且有吸引力\n2. 小节要有逻辑递进关系\n3. 适合{{ $json.audience }}的知识水平\n4. 体现{{ $json.topic }}的核心概念\n\n请严格按照以下JSON格式返回：\n```json\n{\n  \"chapters\": [\n    {\n      \"number\": 1,\n      \"title\": \"章节标题\",\n      \"description\": \"章节简介\",\n      \"sections\": [\n        {\"number\": 1, \"title\": \"小节标题\", \"keyPoints\": [\"要点1\", \"要点2\"]},\n        {\"number\": 2, \"title\": \"小节标题\", \"keyPoints\": [\"要点1\", \"要点2\"]}\n      ]\n    }\n  ]\n}\n```"
      },
      "id": "generate-structure",
      "name": "3️⃣ 生成章节与小节",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [600, 300]
    },
    {
      "parameters": {
        "jsCode": "// 解析章节结构并展开为小节任务\nconst response = $input.first().json.choices[0].message.content;\nconst bookConfig = $('setup-params').first().json;\n\nlet structureData;\ntry {\n  // 尝试提取JSON部分\n  const jsonMatch = response.match(/```json\\s*([\\s\\S]*?)\\s*```/) || response.match(/{[\\s\\S]*}/);\n  if (jsonMatch) {\n    structureData = JSON.parse(jsonMatch[1] || jsonMatch[0]);\n  } else {\n    throw new Error('No JSON found');\n  }\n} catch (e) {\n  // 备用解析方案\n  console.log('JSON解析失败，使用备用方案');\n  structureData = {\n    chapters: [\n      {\n        number: 1,\n        title: '自进化智能体概述',\n        description: '介绍自进化智能体的基本概念',\n        sections: [\n          {number: 1, title: '定义与特征', keyPoints: ['自主学习', '适应性进化']},\n          {number: 2, title: '发展历程', keyPoints: ['技术演进', '里程碑事件']}\n        ]\n      }\n    ]\n  };\n}\n\n// 展开所有小节为独立任务\nconst allSections = [];\nstructureData.chapters.forEach(chapter => {\n  chapter.sections.forEach(section => {\n    allSections.push({\n      ...bookConfig,\n      chapterNumber: chapter.number,\n      chapterTitle: chapter.title,\n      chapterDescription: chapter.description,\n      sectionNumber: section.number,\n      sectionTitle: section.title,\n      sectionKeyPoints: section.keyPoints || [],\n      fullStructure: structureData\n    });\n  });\n});\n\nreturn allSections.map(section => ({ json: section }));"
      },
      "id": "parse-structure",
      "name": "解析章节结构",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [800, 300]
    },
    {
      "parameters": {
        "resource": "chat",
        "operation": "create",
        "chatId": "gpt-4",
        "text": "=请为《{{ $json.topic }}》一书撰写以下小节的详细内容：\n\n**章节信息：**\n- 第{{ $json.chapterNumber }}章：{{ $json.chapterTitle }}\n- 第{{ $json.sectionNumber }}节：{{ $json.sectionTitle }}\n- 关键要点：{{ $json.sectionKeyPoints.join('、') }}\n\n**图书设定：**\n- 目标读者：{{ $json.audience }}\n- 写作风格：{{ $json.style }}\n- 章节描述：{{ $json.chapterDescription }}\n\n**写作要求：**\n1. 内容长度：1000-1500字\n2. 结构：引言(100字) + 正文(1200字) + 小结(200字)\n3. 语言风格：{{ $json.style }}，适合{{ $json.audience }}\n4. 必须包含具体案例和实用见解\n5. 与{{ $json.topic }}主题紧密相关\n6. 逻辑清晰，论证充分\n\n请直接返回正文内容，不要包含任何格式标记或说明文字。"
      },
      "id": "write-content",
      "name": "4️⃣ 写作小节正文",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [1000, 300]
    },
    {
      "parameters": {
        "resource": "chat",
        "operation": "create",
        "chatId": "gpt-3.5-turbo",
        "text": "=请对以下内容进行专业质量评分：\n\n**评分内容：**\n{{ $('write-content').first().json.choices[0].message.content }}\n\n**评分标准（每项1-10分）：**\n1. **内容质量**：专业性、准确性、深度\n2. **逻辑结构**：条理性、连贯性、层次感\n3. **语言表达**：流畅度、可读性、风格一致性\n4. **实用价值**：案例质量、见解独特性、应用性\n\n**请严格按照以下JSON格式返回：**\n```json\n{\n  \"scores\": {\n    \"content\": 分数,\n    \"logic\": 分数,\n    \"language\": 分数,\n    \"practical\": 分数\n  },\n  \"total\": 总分,\n  \"grade\": \"等级(优秀/良好/一般/需改进)\",\n  \"feedback\": \"具体改进建议\",\n  \"highlights\": [\"亮点1\", \"亮点2\"]\n}\n```"
      },
      "id": "rate-content",
      "name": "5️⃣ 自动评分",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [1200, 300]
    },
    {
      "parameters": {
        "jsCode": "// Markdown格式化并合并评分信息\nconst contentResponse = $('write-content').first().json;\nconst ratingResponse = $input.first().json;\nconst sectionData = $('parse-structure').first().json;\n\nconst content = contentResponse.choices[0].message.content;\nlet rating;\ntry {\n  const ratingText = ratingResponse.choices[0].message.content;\n  const jsonMatch = ratingText.match(/```json\\s*([\\s\\S]*?)\\s*```/) || ratingText.match(/{[\\s\\S]*}/);\n  if (jsonMatch) {\n    rating = JSON.parse(jsonMatch[1] || jsonMatch[0]);\n  } else {\n    throw new Error('No rating JSON found');\n  }\n} catch (e) {\n  rating = { total: 8.0, grade: '良好', feedback: '内容质量良好', highlights: ['结构清晰'] };\n}\n\n// 生成标准Markdown格式\nconst markdown = `## 第${sectionData.chapterNumber}章 ${sectionData.chapterTitle}\\n\\n### ${sectionData.chapterNumber}.${sectionData.sectionNumber} ${sectionData.sectionTitle}\\n\\n${content}\\n\\n---\\n\\n> **📊 质量评分**: ${rating.total}/10 (${rating.grade})  \\n> **💡 亮点**: ${(rating.highlights || []).join('、')}  \\n> **📝 建议**: ${rating.feedback}\\n\\n`;\n\nreturn {\n  json: {\n    ...sectionData,\n    content: content,\n    rating: rating,\n    markdown: markdown,\n    wordCount: content.split(/\\s+/).length,\n    chapterSection: `${sectionData.chapterNumber}-${sectionData.sectionNumber}`\n  }\n};"
      },
      "id": "format-markdown",
      "name": "6️⃣ Markdown排版",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1400, 300]
    },
    {
      "parameters": {
        "mode": "mergeByIndex",
        "options": {}
      },
      "id": "collect-sections",
      "name": "收集所有小节",
      "type": "n8n-nodes-base.itemLists",
      "typeVersion": 3,
      "position": [1600, 300]
    },
    {
      "parameters": {
        "jsCode": "// 生成完整目录页\nconst allSections = $input.all();\nconst bookConfig = allSections[0].json;\nconst fullStructure = bookConfig.fullStructure;\n\n// 生成封面和目录\nlet toc = `# ${bookConfig.topic}\\n\\n`;\ntoc += `**作者**: ${bookConfig.author}  \\n`;\ntoc += `**适合读者**: ${bookConfig.audience}  \\n`;\ntoc += `**写作风格**: ${bookConfig.style}  \\n`;\ntoc += `**生成时间**: ${new Date(bookConfig.timestamp).toLocaleString()}  \\n\\n`;\ntoc += `---\\n\\n## 📚 目录\\n\\n`;\n\n// 按章节组织目录\nfullStructure.chapters.forEach(chapter => {\n  toc += `### 第${chapter.number}章 ${chapter.title}\\n\\n`;\n  chapter.sections.forEach(section => {\n    const anchor = `第${chapter.number}章-${chapter.number}${section.number}-${section.title}`.replace(/[\\s\\W]/g, '-');\n    toc += `- [${chapter.number}.${section.number} ${section.title}](#${anchor})\\n`;\n  });\n  toc += '\\n';\n});\n\ntoc += `---\\n\\n`;\n\nreturn { json: { toc, allSections: allSections.map(item => item.json), bookConfig } };"
      },
      "id": "generate-toc",
      "name": "7️⃣ 生成目录页",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1800, 300]
    },
    {
      "parameters": {
        "jsCode": "// 拼接全文Markdown\nconst data = $input.first().json;\nconst { toc, allSections } = data;\n\n// 按章节和小节排序\nallSections.sort((a, b) => {\n  if (a.chapterNumber !== b.chapterNumber) {\n    return a.chapterNumber - b.chapterNumber;\n  }\n  return a.sectionNumber - b.sectionNumber;\n});\n\n// 拼接完整文档\nlet fullMarkdown = toc;\nallSections.forEach(section => {\n  fullMarkdown += section.markdown;\n});\n\nreturn { json: { ...data, fullMarkdown } };"
      },
      "id": "merge-full-text",
      "name": "8️⃣ 拼接全文",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [2000, 300]
    },
    {
      "parameters": {
        "jsCode": "// 添加详细统计信息\nconst data = $input.first().json;\nconst { fullMarkdown, allSections, bookConfig } = data;\n\n// 计算统计数据\nconst totalWords = allSections.reduce((sum, section) => sum + section.wordCount, 0);\nconst avgRating = allSections.reduce((sum, section) => sum + section.rating.total, 0) / allSections.length;\nconst chapterCount = new Set(allSections.map(s => s.chapterNumber)).size;\nconst readingTime = Math.ceil(totalWords / 300);\nconst completionTime = new Date().toISOString();\n\n// 质量分布统计\nconst qualityDistribution = allSections.reduce((acc, section) => {\n  const grade = section.rating.grade || '良好';\n  acc[grade] = (acc[grade] || 0) + 1;\n  return acc;\n}, {});\n\n// 生成统计信息页面\nconst statsMarkdown = `\\n\\n---\\n\\n## 📊 图书统计报告\\n\\n### 基本信息\\n- **总字数**: ${totalWords.toLocaleString()} 字\\n- **章节数**: ${chapterCount} 章\\n- **小节数**: ${allSections.length} 节\\n- **预计阅读时间**: ${readingTime} 分钟\\n\\n### 质量评估\\n- **平均评分**: ${avgRating.toFixed(1)}/10\\n- **质量分布**: ${Object.entries(qualityDistribution).map(([grade, count]) => `${grade}(${count}节)`).join('、')}\\n\\n### 时间记录\\n- **开始时间**: ${new Date(bookConfig.timestamp).toLocaleString()}\\n- **完成时间**: ${new Date(completionTime).toLocaleString()}\\n- **总耗时**: ${Math.round((new Date(completionTime) - new Date(bookConfig.timestamp)) / 1000 / 60)} 分钟\\n\\n---\\n\\n*📖 本书由AI智能写作助手自动生成*  \\n*🤖 基于用户需求: "${bookConfig.userInput}"*`;\n\nconst finalMarkdown = fullMarkdown + statsMarkdown;\n\nconst stats = {\n  totalWords,\n  avgRating: parseFloat(avgRating.toFixed(1)),\n  chapterCount,\n  sectionCount: allSections.length,\n  readingTime,\n  completionTime,\n  qualityDistribution\n};\n\nreturn { json: { ...data, finalMarkdown, stats } };"
      },
      "id": "add-statistics",
      "name": "9️⃣ 添加统计信息",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [2200, 300]
    },
    {
      "parameters": {
        "url": "https://api.html2pdf.app/v1/generate",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "Authorization",
              "value": "Bearer YOUR_HTML2PDF_API_KEY"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonBody": "={ \"html\": `<html><head><meta charset='utf-8'><title>${$json.bookConfig.topic}</title><style>body{font-family:'Microsoft YaHei',Arial,sans-serif;line-height:1.8;margin:40px;color:#333;} h1{color:#2c3e50;border-bottom:3px solid #3498db;padding-bottom:10px;} h2{color:#34495e;margin-top:30px;} h3{color:#7f8c8d;} blockquote{background:#f8f9fa;border-left:4px solid #3498db;padding:15px;margin:20px 0;} pre,code{background:#f4f4f4;padding:10px;border-radius:5px;font-family:Consolas,monospace;} hr{border:none;height:2px;background:#ecf0f1;margin:30px 0;} .stats{background:#e8f5e8;padding:20px;border-radius:8px;margin:20px 0;}</style></head><body>${$json.finalMarkdown.replace(/\\n/g, '<br>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/^### (.*$)/gm, '<h3>$1</h3>').replace(/^## (.*$)/gm, '<h2>$1</h2>').replace(/^# (.*$)/gm, '<h1>$1</h1>').replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>').replace(/^---$/gm, '<hr>')}</body></html>`, \"options\": { \"format\": \"A4\", \"margin\": { \"top\": \"20mm\", \"bottom\": \"20mm\", \"left\": \"15mm\", \"right\": \"15mm\" } } }"
      },
      "id": "generate-pdf",
      "name": "🔟 生成PDF版本",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4,
      "position": [2400, 300]
    },
    {
      "parameters": {
        "authentication": "oAuth2",
        "resource": "file",
        "operation": "upload",
        "binaryData": true,
        "fileName": "={{ $('add-statistics').first().json.bookConfig.topic.replace(/[^\\w\\u4e00-\\u9fa5]/g, '_') }}_{{ new Date().toISOString().split('T')[0] }}.pdf",
        "folderId": "YOUR_GOOGLE_DRIVE_FOLDER_ID",
        "options": {
          "description": "=AI自动生成图书：{{ $('add-statistics').first().json.bookConfig.topic }} | 字数：{{ $('add-statistics').first().json.stats.totalWords }} | 评分：{{ $('add-statistics').first().json.stats.avgRating }}/10"
        }
      },
      "id": "upload-drive",
      "name": "1️⃣1️⃣ 上传Google Drive",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [2600, 300]
    },
    {
      "parameters": {
        "fromEmail": "<EMAIL>",
        "toEmail": "<EMAIL>",
        "subject": "=📚 《{{ $('add-statistics').first().json.bookConfig.topic }}》生成完成",
        "emailType": "html",
        "message": "=<html><body style='font-family:Arial,sans-serif;line-height:1.6;color:#333;'><div style='max-width:600px;margin:0 auto;padding:20px;'><h2 style='color:#2c3e50;border-bottom:2px solid #3498db;padding-bottom:10px;'>📚 新书生成完成！</h2><div style='background:#f8f9fa;padding:20px;border-radius:8px;margin:20px 0;'><h3 style='color:#34495e;margin-top:0;'>📖 图书信息</h3><p><strong>书名</strong>: {{ $('add-statistics').first().json.bookConfig.topic }}</p><p><strong>作者</strong>: {{ $('add-statistics').first().json.bookConfig.author }}</p><p><strong>目标读者</strong>: {{ $('add-statistics').first().json.bookConfig.audience }}</p><p><strong>写作风格</strong>: {{ $('add-statistics').first().json.bookConfig.style }}</p></div><div style='background:#e8f5e8;padding:20px;border-radius:8px;margin:20px 0;'><h3 style='color:#27ae60;margin-top:0;'>📊 统计数据</h3><p><strong>总字数</strong>: {{ $('add-statistics').first().json.stats.totalWords.toLocaleString() }} 字</p><p><strong>章节数</strong>: {{ $('add-statistics').first().json.stats.chapterCount }} 章</p><p><strong>小节数</strong>: {{ $('add-statistics').first().json.stats.sectionCount }} 节</p><p><strong>平均评分</strong>: {{ $('add-statistics').first().json.stats.avgRating }}/10</p><p><strong>预计阅读时间</strong>: {{ $('add-statistics').first().json.stats.readingTime }} 分钟</p></div><div style='background:#fff3cd;padding:20px;border-radius:8px;margin:20px 0;'><h3 style='color:#856404;margin-top:0;'>📁 文件信息</h3><p><strong>Google Drive链接</strong>: <a href='{{ $('upload-drive').first().json.webViewLink }}' style='color:#3498db;'>点击查看PDF文件</a></p><p><strong>文件大小</strong>: {{ ($('upload-drive').first().json.size / 1024 / 1024).toFixed(2) }} MB</p></div><div style='text-align:center;margin-top:30px;padding-top:20px;border-top:1px solid #eee;'><p style='color:#7f8c8d;font-size:14px;'>🤖 本邮件由AI智能写作助手自动发送<br>生成时间: {{ new Date().toLocaleString() }}</p></div></div></body></html>",
        "options": {}
      },
      "id": "send-email",
      "name": "1️⃣2️⃣ 邮件通知",
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [2800, 300]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={ \"success\": true, \"message\": \"《{{ $('add-statistics').first().json.bookConfig.topic }}》生成完成\", \"data\": { \"bookTitle\": \"{{ $('add-statistics').first().json.bookConfig.topic }}\", \"stats\": {{ JSON.stringify($('add-statistics').first().json.stats) }}, \"driveUrl\": \"{{ $('upload-drive').first().json.webViewLink }}\", \"downloadUrl\": \"{{ $('upload-drive').first().json.webContentLink }}\" } }"
      },
      "id": "webhook-response",
      "name": "返回结果",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [3000, 300]
    }
  ],
  "connections": {
    "1️⃣ 聊天对话接收输入": {
      "main": [[{"node": "2️⃣ 设置写作参数", "type": "main", "index": 0}]]
    },
    "2️⃣ 设置写作参数": {
      "main": [[{"node": "3️⃣ 生成章节与小节", "type": "main", "index": 0}]]
    },
    "3️⃣ 生成章节与小节": {
      "main": [[{"node": "解析章节结构", "type": "main", "index": 0}]]
    },
    "解析章节结构": {
      "main": [[{"node": "4️⃣ 写作小节正文", "type": "main", "index": 0}]]
    },
    "4️⃣ 写作小节正文": {
      "main": [[{"node": "5️⃣ 自动评分", "type": "main", "index": 0}]]
    },
    "5️⃣ 自动评分": {
      "main": [[{"node": "6️⃣ Markdown排版", "type": "main", "index": 0}]]
    },
    "6️⃣ Markdown排版": {
      "main": [[{"node": "收集所有小节", "type": "main", "index": 0}]]
    },
    "收集所有小节": {
      "main": [[{"node": "7️⃣ 生成目录页", "type": "main", "index": 0}]]
    },
    "7️⃣ 生成目录页": {
      "main": [[{"node": "8️⃣ 拼接全文", "type": "main", "index": 0}]]
    },
    "8️⃣ 拼接全文": {
      "main": [[{"node": "9️⃣ 添加统计信息", "type": "main", "index": 0}]]
    },
    "9️⃣ 添加统计信息": {
      "main": [[{"node": "🔟 生成PDF版本", "type": "main", "index": 0}]]
    },
    "🔟 生成PDF版本": {
      "main": [[{"node": "1️⃣1️⃣ 上传Google Drive", "type": "main", "index": 0}]]
    },
    "1️⃣1️⃣ 上传Google Drive": {
      "main": [[{"node": "1️⃣2️⃣ 邮件通知", "type": "main", "index": 0}]]
    },
    "1️⃣2️⃣ 邮件通知": {
      "main": [[{"node": "返回结果", "type": "main", "index": 0}]]
    }
  }
}
