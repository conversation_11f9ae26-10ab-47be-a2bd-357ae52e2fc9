# 🤖 n8n智能写书Agent使用指南

## 📋 工作流概述

这是一个完整的AI写书自动化工作流，专门优化用于生成"自进化智能体"主题的专业图书。工作流包含12个核心步骤，从用户输入到最终PDF生成和通知。

## 🔧 配置要求

### 必需的API密钥和服务
1. **OpenAI API Key** - 用于GPT-4内容生成
2. **HTML2PDF API Key** - 用于PDF转换
3. **Google Drive OAuth** - 用于文件上传
4. **SMTP邮件服务** - 用于通知邮件

### 环境变量设置
```bash
OPENAI_API_KEY=your_openai_api_key
HTML2PDF_API_KEY=your_html2pdf_api_key
GOOGLE_DRIVE_FOLDER_ID=your_folder_id
SMTP_HOST=your_smtp_host
SMTP_USER=your_email
SMTP_PASS=your_password
```

## 🚀 使用方法

### 1. 导入工作流
1. 在n8n中点击"Import from file"
2. 选择 `optimized_book_writer_workflow.json`
3. 确认导入所有节点

### 2. 配置API连接
- **OpenAI节点**: 添加OpenAI凭据
- **Google Drive节点**: 配置OAuth2认证
- **Email节点**: 设置SMTP服务器信息
- **HTTP Request节点**: 添加HTML2PDF API密钥

### 3. 启动工作流
发送POST请求到webhook端点：
```bash
curl -X POST https://your-n8n-instance.com/webhook/chat-book-writer \
  -H "Content-Type: application/json" \
  -d '{
    "message": "我想写一本关于自进化智能体的深度技术书籍",
    "audience": "AI研究者",
    "chapters": 10,
    "style": "学术深度"
  }'
```

## 📊 工作流步骤详解

### 1️⃣ 聊天对话接收输入
- **功能**: 接收用户的写书需求
- **默认主题**: "自进化智能体"
- **支持参数**: 主题、读者群体、章节数、写作风格

### 2️⃣ 设置写作参数
- **智能解析**: 根据关键词自动调整参数
- **默认配置**: 8章节，面向技术研究者
- **风格适配**: 根据读者群体调整写作风格

### 3️⃣ 生成章节与小节
- **AI策划**: 使用GPT-4生成完整图书结构
- **逻辑递进**: 确保章节间的逻辑关系
- **小节细化**: 每章3-4个小节，包含关键要点

### 4️⃣ 写作小节正文
- **内容长度**: 1000-1500字/小节
- **结构标准**: 引言+正文+小结
- **案例丰富**: 包含具体实例和实用见解

### 5️⃣ 自动评分
- **多维评分**: 内容质量、逻辑结构、语言表达、实用价值
- **等级评定**: 优秀/良好/一般/需改进
- **改进建议**: 提供具体的优化建议

### 6️⃣ Markdown排版
- **标准格式**: 章节标题、小节标题、分隔线
- **质量标注**: 每小节附带评分和建议
- **超链接**: 支持目录跳转

### 7️⃣ 生成目录页
- **完整目录**: 包含所有章节和小节
- **超链接支持**: 点击可跳转到对应内容
- **图书信息**: 作者、读者群体、风格等

### 8️⃣ 拼接全文
- **逻辑排序**: 按章节和小节顺序组织
- **格式统一**: 保持一致的Markdown格式
- **内容完整**: 包含目录、正文、评分

### 9️⃣ 添加统计信息
- **字数统计**: 总字数、章节数、小节数
- **质量分析**: 平均评分、质量分布
- **时间记录**: 生成耗时、完成时间

### 🔟 生成PDF版本
- **专业排版**: A4格式，适当边距
- **样式优化**: 中文字体、层次清晰
- **格式转换**: Markdown转HTML再转PDF

### 1️⃣1️⃣ 上传Google Drive
- **自动命名**: 主题_日期.pdf
- **文件描述**: 包含字数和评分信息
- **权限设置**: 可配置共享权限

### 1️⃣2️⃣ 邮件通知
- **HTML邮件**: 美观的邮件模板
- **详细信息**: 图书统计、下载链接
- **自动发送**: 完成后立即通知

## 🎯 使用场景

### 学术研究
```json
{
  "message": "自进化智能体的理论基础与实现方法",
  "audience": "AI研究者",
  "chapters": 12,
  "style": "学术深度"
}
```

### 技术入门
```json
{
  "message": "自进化智能体入门指南",
  "audience": "初学者", 
  "chapters": 6,
  "style": "通俗易懂"
}
```

### 行业应用
```json
{
  "message": "自进化智能体在企业中的应用实践",
  "audience": "企业技术人员",
  "chapters": 8,
  "style": "实用导向"
}
```

## 📈 输出示例

### 成功响应
```json
{
  "success": true,
  "message": "《自进化智能体理论与实践》生成完成",
  "data": {
    "bookTitle": "自进化智能体理论与实践",
    "stats": {
      "totalWords": 12500,
      "avgRating": 8.7,
      "chapterCount": 8,
      "sectionCount": 24,
      "readingTime": 42
    },
    "driveUrl": "https://drive.google.com/file/d/...",
    "downloadUrl": "https://drive.google.com/uc?id=..."
  }
}
```

## 🔧 自定义配置

### 修改默认主题
在"设置写作参数"节点中修改：
```javascript
let topic = '你的默认主题';
```

### 调整内容长度
在"写作小节正文"节点中修改：
```
内容长度：800-1200字  // 可调整
```

### 更换PDF服务
可替换HTML2PDF为其他PDF生成服务，如Puppeteer、wkhtmltopdf等。

## ⚠️ 注意事项

1. **API配额**: 注意OpenAI API的使用限制
2. **生成时间**: 完整图书生成需要10-30分钟
3. **文件大小**: PDF文件通常在2-10MB
4. **质量控制**: 建议人工审核AI生成的内容
5. **版权问题**: 确保生成内容的原创性

## 🆘 故障排除

### 常见问题
1. **OpenAI超时**: 增加timeout设置或分批处理
2. **PDF生成失败**: 检查HTML2PDF API密钥和格式
3. **邮件发送失败**: 验证SMTP配置
4. **Google Drive上传失败**: 检查OAuth权限

### 调试建议
- 启用n8n的执行日志
- 检查每个节点的输出数据
- 验证API密钥的有效性
- 监控API使用配额

---

🎉 **现在您可以开始使用这个强大的AI写书工具了！**
