{"name": "自进化智能体写书工作流", "nodes": [{"parameters": {"options": {"loadPreviousSession": true, "sessionIdType": "customKey", "sessionKey": "={{ $json.sessionId || 'book-writer-session' }}"}}, "id": "369686c5-30de-49e7-a004-831161f17191", "name": "1️⃣ 聊天对话接收输入", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [200, 300], "webhookId": "97d60090-1717-4195-bf27-2e3c3105b7f8", "typeVersion": 1.1}, {"parameters": {"jsCode": "const input = $input.first().json; const userMessage = input.chatInput || input.message || input.text || input.topic || ''; let topic = '自进化智能体'; let audience = '技术研究者'; let chapters = 8; let style = '学术通俗'; if (userMessage && userMessage.length > 5) { topic = userMessage; } if (userMessage.includes('入门') || userMessage.includes('初学')) { audience = '初学者'; style = '通俗易懂'; chapters = 6; } else if (userMessage.includes('深入') || userMessage.includes('高级')) { audience = '专业人士'; style = '深度技术'; chapters = 10; } const bookConfig = { topic: topic, audience: audience, chapters: chapters, style: style, language: '中文', author: 'AI智能助手', timestamp: new Date().toISOString(), userInput: userMessage, sessionId: input.sessionId || 'book-writer-session' }; return { json: bookConfig };"}, "id": "setup-params", "name": "2️⃣ 设置写作参数", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-4", "text": "=作为AI图书策划专家，请为主题「{{ $json.topic }}」设计一本{{ $json.chapters }}章的专业图书大纲。目标读者：{{ $json.audience }}，写作风格：{{ $json.style }}。请设计章节结构，每章包含3-4个小节。请严格按照JSON格式返回章节信息。"}, "id": "generate-structure", "name": "3️⃣ 生成章节与小节", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [600, 300]}, {"parameters": {"jsCode": "const response = $input.first().json.choices[0].message.content; const bookConfig = $('setup-params').first().json; let structureData; try { const jsonMatch = response.match(/{[\\s\\S]*}/); if (jsonMatch) { structureData = JSON.parse(jsonMatch[0]); } else { throw new Error('No JSON found'); } } catch (e) { structureData = { chapters: [{ number: 1, title: '自进化智能体概述', description: '介绍自进化智能体的基本概念', sections: [{number: 1, title: '定义与特征', keyPoints: ['自主学习', '适应性进化']}, {number: 2, title: '发展历程', keyPoints: ['技术演进', '里程碑事件']}] }] }; } const allSections = []; structureData.chapters.forEach(chapter => { chapter.sections.forEach(section => { allSections.push({ ...bookConfig, chapterNumber: chapter.number, chapterTitle: chapter.title, chapterDescription: chapter.description, sectionNumber: section.number, sectionTitle: section.title, sectionKeyPoints: section.keyPoints || [], fullStructure: structureData }); }); }); return allSections.map(section => ({ json: section }));"}, "id": "parse-structure", "name": "解析章节结构", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 300]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-4", "text": "=请为《{{ $json.topic }}》一书撰写第{{ $json.chapterNumber }}章第{{ $json.sectionNumber }}节的详细内容。章节：{{ $json.chapterTitle }}，小节：{{ $json.sectionTitle }}，关键要点：{{ $json.sectionKeyPoints.join('、') }}。目标读者：{{ $json.audience }}，写作风格：{{ $json.style }}。要求：1000-1500字，结构清晰，包含具体案例。请直接返回正文内容。"}, "id": "write-content", "name": "4️⃣ 写作小节正文", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-3.5-turbo", "text": "=请对以下内容进行质量评分（1-10分）：{{ $('write-content').first().json.choices[0].message.content }}。评分维度：内容质量、逻辑结构、语言表达、实用价值。请以JSON格式返回评分结果。"}, "id": "rate-content", "name": "5️⃣ 自动评分", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1200, 300]}, {"parameters": {"jsCode": "const contentResponse = $('write-content').first().json; const ratingResponse = $input.first().json; const sectionData = $('parse-structure').first().json; const content = contentResponse.choices[0].message.content; let rating; try { const ratingText = ratingResponse.choices[0].message.content; const jsonMatch = ratingText.match(/{[\\s\\S]*}/); if (jsonMatch) { rating = JSON.parse(jsonMatch[0]); } else { throw new Error('No rating JSON found'); } } catch (e) { rating = { total: 8.0, grade: '良好', feedback: '内容质量良好', highlights: ['结构清晰'] }; } const markdown = `## 第${sectionData.chapterNumber}章 ${sectionData.chapterTitle}\\n\\n### ${sectionData.chapterNumber}.${sectionData.sectionNumber} ${sectionData.sectionTitle}\\n\\n${content}\\n\\n---\\n\\n> **📊 质量评分**: ${rating.total}/10 (${rating.grade})\\n\\n`; return { json: { ...sectionData, content: content, rating: rating, markdown: markdown, wordCount: content.split(/\\s+/).length, chapterSection: `${sectionData.chapterNumber}-${sectionData.sectionNumber}` } };"}, "id": "format-markdown", "name": "6️⃣ Markdown排版", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1400, 300]}, {"parameters": {"mode": "mergeByIndex", "options": {}}, "id": "collect-sections", "name": "收集所有小节", "type": "n8n-nodes-base.itemLists", "typeVersion": 3, "position": [1600, 300]}, {"parameters": {"jsCode": "const allSections = $input.all(); const bookConfig = allSections[0].json; const fullStructure = bookConfig.fullStructure; let toc = `# ${bookConfig.topic}\\n\\n**作者**: ${bookConfig.author}\\n**适合读者**: ${bookConfig.audience}\\n**写作风格**: ${bookConfig.style}\\n\\n---\\n\\n## 📚 目录\\n\\n`; fullStructure.chapters.forEach(chapter => { toc += `### 第${chapter.number}章 ${chapter.title}\\n`; chapter.sections.forEach(section => { toc += `- ${chapter.number}.${section.number} ${section.title}\\n`; }); toc += '\\n'; }); toc += `---\\n\\n`; return { json: { toc, allSections: allSections.map(item => item.json), bookConfig } };"}, "id": "generate-toc", "name": "7️⃣ 生成目录页", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1800, 300]}, {"parameters": {"jsCode": "const data = $input.first().json; const { toc, allSections } = data; allSections.sort((a, b) => { if (a.chapterNumber !== b.chapterNumber) { return a.chapterNumber - b.chapterNumber; } return a.sectionNumber - b.sectionNumber; }); let fullMarkdown = toc; allSections.forEach(section => { fullMarkdown += section.markdown; }); return { json: { ...data, fullMarkdown } };"}, "id": "merge-full-text", "name": "8️⃣ 拼接全文", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"jsCode": "const data = $input.first().json; const { fullMarkdown, allSections, bookConfig } = data; const totalWords = allSections.reduce((sum, section) => sum + section.wordCount, 0); const avgRating = allSections.reduce((sum, section) => sum + section.rating.total, 0) / allSections.length; const chapterCount = new Set(allSections.map(s => s.chapterNumber)).size; const readingTime = Math.ceil(totalWords / 300); const completionTime = new Date().toISOString(); const statsMarkdown = `\\n\\n---\\n\\n## 📊 图书统计报告\\n\\n- **总字数**: ${totalWords.toLocaleString()} 字\\n- **章节数**: ${chapterCount} 章\\n- **小节数**: ${allSections.length} 节\\n- **平均评分**: ${avgRating.toFixed(1)}/10\\n- **预计阅读时间**: ${readingTime} 分钟\\n- **生成时间**: ${new Date(bookConfig.timestamp).toLocaleString()}\\n- **完成时间**: ${new Date(completionTime).toLocaleString()}\\n\\n---\\n\\n*📖 本书由AI智能写作助手自动生成*`; const finalMarkdown = fullMarkdown + statsMarkdown; const stats = { totalWords, avgRating: parseFloat(avgRating.toFixed(1)), chapterCount, sectionCount: allSections.length, readingTime, completionTime }; return { json: { ...data, finalMarkdown, stats } };"}, "id": "add-statistics", "name": "9️⃣ 添加统计信息", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2200, 300]}, {"parameters": {"url": "https://api.html2pdf.app/v1/generate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer YOUR_HTML2PDF_API_KEY"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={ \"html\": `<html><head><meta charset='utf-8'><title>${$json.bookConfig.topic}</title><style>body{font-family:Arial,sans-serif;line-height:1.6;margin:40px;color:#333;} h1,h2,h3{color:#2c3e50;} hr{border:none;height:2px;background:#ecf0f1;margin:30px 0;}</style></head><body>${$json.finalMarkdown.replace(/\\n/g, '<br>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/^### (.*$)/gm, '<h3>$1</h3>').replace(/^## (.*$)/gm, '<h2>$1</h2>').replace(/^# (.*$)/gm, '<h1>$1</h1>')}</body></html>`, \"options\": { \"format\": \"A4\" } }"}, "id": "generate-pdf", "name": "🔟 生成PDF版本", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [2400, 300]}, {"parameters": {"authentication": "oAuth2", "resource": "file", "operation": "upload", "binaryData": true, "fileName": "={{ $('add-statistics').first().json.bookConfig.topic.replace(/[^\\w\\u4e00-\\u9fa5]/g, '_') }}_{{ new Date().toISOString().split('T')[0] }}.pdf", "folderId": "YOUR_GOOGLE_DRIVE_FOLDER_ID", "options": {"description": "=AI自动生成图书：{{ $('add-statistics').first().json.bookConfig.topic }}"}}, "id": "upload-drive", "name": "1️⃣1️⃣ 上传Google Drive", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2600, 300]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "=📚 《{{ $('add-statistics').first().json.bookConfig.topic }}》生成完成", "emailType": "text", "message": "=新书《{{ $('add-statistics').first().json.bookConfig.topic }}》已生成完成！\\n\\n统计信息：\\n- 总字数：{{ $('add-statistics').first().json.stats.totalWords }} 字\\n- 章节数：{{ $('add-statistics').first().json.stats.chapterCount }} 章\\n- 平均评分：{{ $('add-statistics').first().json.stats.avgRating }}/10\\n\\nGoogle Drive链接：{{ $('upload-drive').first().json.webViewLink }}", "options": {}}, "id": "send-email", "name": "1️⃣2️⃣ 邮件通知", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2800, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={ \"success\": true, \"message\": \"《{{ $('add-statistics').first().json.bookConfig.topic }}》生成完成\", \"data\": { \"bookTitle\": \"{{ $('add-statistics').first().json.bookConfig.topic }}\", \"stats\": {{ JSON.stringify($('add-statistics').first().json.stats) }}, \"driveUrl\": \"{{ $('upload-drive').first().json.webViewLink }}\" } }"}, "id": "webhook-response", "name": "返回结果", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [3000, 300]}], "connections": {"1️⃣ 聊天对话接收输入": {"main": [[{"node": "2️⃣ 设置写作参数", "type": "main", "index": 0}]]}, "2️⃣ 设置写作参数": {"main": [[{"node": "3️⃣ 生成章节与小节", "type": "main", "index": 0}]]}, "3️⃣ 生成章节与小节": {"main": [[{"node": "解析章节结构", "type": "main", "index": 0}]]}, "解析章节结构": {"main": [[{"node": "4️⃣ 写作小节正文", "type": "main", "index": 0}]]}, "4️⃣ 写作小节正文": {"main": [[{"node": "5️⃣ 自动评分", "type": "main", "index": 0}]]}, "5️⃣ 自动评分": {"main": [[{"node": "6️⃣ Markdown排版", "type": "main", "index": 0}]]}, "6️⃣ Markdown排版": {"main": [[{"node": "收集所有小节", "type": "main", "index": 0}]]}, "收集所有小节": {"main": [[{"node": "7️⃣ 生成目录页", "type": "main", "index": 0}]]}, "7️⃣ 生成目录页": {"main": [[{"node": "8️⃣ 拼接全文", "type": "main", "index": 0}]]}, "8️⃣ 拼接全文": {"main": [[{"node": "9️⃣ 添加统计信息", "type": "main", "index": 0}]]}, "9️⃣ 添加统计信息": {"main": [[{"node": "🔟 生成PDF版本", "type": "main", "index": 0}]]}, "🔟 生成PDF版本": {"main": [[{"node": "1️⃣1️⃣ 上传Google Drive", "type": "main", "index": 0}]]}, "1️⃣1️⃣ 上传Google Drive": {"main": [[{"node": "1️⃣2️⃣ 邮件通知", "type": "main", "index": 0}]]}, "1️⃣2️⃣ 邮件通知": {"main": [[{"node": "返回结果", "type": "main", "index": 0}]]}}}