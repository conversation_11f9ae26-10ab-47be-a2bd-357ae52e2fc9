%!TEX encoding = UTF-8 Unicode
\documentclass[openany]{book}

\title{动态自进化智能体系统架构实践}
% \author{作者}

\input{package}
\input{option}
\input{hack}

% 水印包 - 预览版本专用
\usepackage{draftwatermark}

% 水印设置 - 预览版本专用
\SetWatermarkText{预览版 | 18810901685}
\SetWatermarkScale{0.6}
\SetWatermarkAngle{45}
\SetWatermarkColor{gray!25}
\SetWatermarkLightness{0.8}

\usepackage{indentfirst}

\usepackage{amsthm}
\usepackage{amsmath}
\usepackage{tabularx}   % 放在导言区
\usepackage{array} 
\usepackage{graphicx}
\usepackage{subcaption}
\usepackage{caption}
\usepackage{float}
\renewcommand{\tabularxcolumn}[1]{m{#1}}
\newtheorem{thm}{Theorem}
\newcommand\enchapter[1]{\def\@enchapter{#1}}
\setmainfont{Times New Roman}
\setsansfont{Arial}
\setmonofont[Scale=.9]{Courier Std}
\setCJKmonofont[Scale=.9]{FZKai-Z03}
\newfontfamily{\MyriadProLight}{Myriad Pro Light}

\DeclareSymbolFont{numbers}{OT1}{txr}{m}{n}
\SetSymbolFont{numbers}{normal}{OT1}{txr}{m}{n}
\SetSymbolFont{numbers}{bold}{OT1}{txr}{bx}{n}
\DeclareMathSymbol{0}\mathalpha{numbers}{"30}
\DeclareMathSymbol{1}\mathalpha{numbers}{"31}
\DeclareMathSymbol{2}\mathalpha{numbers}{"32}
\DeclareMathSymbol{3}\mathalpha{numbers}{"33}
\DeclareMathSymbol{4}\mathalpha{numbers}{"34}
\DeclareMathSymbol{5}\mathalpha{numbers}{"35}
\DeclareMathSymbol{6}\mathalpha{numbers}{"36}
\DeclareMathSymbol{7}\mathalpha{numbers}{"37}
\DeclareMathSymbol{8}\mathalpha{numbers}{"38}
\DeclareMathSymbol{9}\mathalpha{numbers}{"39}

\newCJKfontfamily{\FZDBSK}{FZDaBiaoSong-B06S}
\newCJKfontfamily{\FZXH1K}{FZXiHeiI-Z08}

\setlength\parskip{1em plus 2pt minus 1pt}
\setstretch{1.5}

\makeatletter
\newpagestyle{main}[\FZXH1K\sffamily\fontsize{9}{9}\selectfont]{
  \sethead[\@title][][]{}{}{\thechapter\quad\chaptertitle}
  \setfoot[\thepage][][]{}{}{\thepage}
}
\newpagestyle{special}[\FZXH1K\sffamily\fontsize{9}{9}\selectfont]{
  \sethead[\chaptertitle][][]{}{}{\chaptertitle}
  \setfoot[\thepage][][]{}{}{\thepage}
}
\makeatother


\setlength{\intextsep}{10pt plus 2pt minus 2pt}%
\usepackage{tocloft}
\renewcommand{\cftbeforetoctitleskip}{0pt}     % 目录标题前间距
\renewcommand{\cftaftertoctitleskip}{1em}      % 目录标题后（与目录项之间）间距

% \usepackage{empheq}
% \usepackage{cases}
% \usepackage{mathrsfs}
\newfontfamily\listingsfontinline[Scale=0.8]{Courier Std}

\lstset{
   numbers=none,
   stepnumber=1,
   basicstyle=\ttfamily\small,
   upquote = true,
   numberstyle=\tiny\color{blue},
   keepspaces=false,
   breaklines=true,
   tabsize=4,
   captionpos=b,
   keywordstyle=\color[rgb]{0, 0, 1},
   commentstyle=\color{green},
   stringstyle=\color{black},
   breaklines=true,
   escapeinside={(*}{*)},
   xleftmargin=3.8em,
   xrightmargin=2em,
   aboveskip=1em,
   showstringspaces=false
}

%%% define common math terms
\providecommand{\Cov}{\ensuremath{\mathrm{Cov}}}
\providecommand{\Var}{\ensuremath{\mathrm{Var}}}
\providecommand{\E}{\ensuremath{\mathrm{E}}}
\DeclareTextFontCommand{\bfemph}{ \bfseries\em}

\allowdisplaybreaks[4]

\begin{document}
\frontmatter
% \addtocontents{toc}{\protect\setcounter{tocdepth}{-1}}
\hypersetup{bookmarksdepth=0}
\pagestyle{empty}

\pdfbookmark[0]{扉页}{titlepage}
\include{title}

\pdfbookmark[0]{版权页}{copyright}
\include{34081-copyright-1-02}

%\pagestyle{special}
%\input{thanks}
%\input{comment}
%\input{author}
%\input{foreword}
%\input{foreword01}
%\input{reader}





\mainmatter
\addtocontents{toc}{\protect\setcounter{tocdepth}{1}}
\hypersetup{bookmarksdepth=2}
\pagestyle{main}


\input{chapters/intro}
\input{chapters/chapter1}
\input{chapters/chapter2}
\input{chapters/chapter3}
\input{chapters/chapter4}
\input{chapters/chapter5}
\input{chapters/chapter6}
\input{chapters/chapter7}
\input{chapters/chapter8}
\input{chapters/chapter9}
\input{chapters/chapter10}
\input{chapters/chapter11}
\input{chapters/chapter12}
% \input{chapters/chapter13}

\backmatter
\addtocontents{toc}{\protect\setcounter{tocdepth}{0}}
\hypersetup{bookmarksdepth=0}
\pagestyle{special}

% 设置参考文献的格式
% \renewcommand{\bibname}{参考文献}
% \renewcommand{\bibsection}{\chapter*{\bibname}\vspace{5em}}
% \addcontentsline{toc}{chapter}{参考文献}

% % 设置参考文献的间距
% \setlength{\bibsep}{1em}
% \setlength{\bibhang}{2em}

% \bibliographystyle{gbt7714-numerical}
% \bibliography{reference}

\end{document}
